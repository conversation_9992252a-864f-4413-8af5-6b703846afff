package fm.lizhi.ocean.seal.manager;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.mapper.GameAppBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.GameCallbackMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created in 2022-04-18 17:57.
 *
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GameAppManager {
    private static final Logger logger = LoggerFactory.getLogger(GameAppManager.class);
    @Inject
    private GameCallbackMapper gameCallbackMapper;
    @Inject
    private GameAppBeanMapper gameAppBeanMapper;
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameChannelManager gameChannelManager;

    private final LoadingCache<String, GameAppBean> gameAppBeanCache = CacheBuilder.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(10)
            .build(new CacheLoader<String, GameAppBean>() {
                @Override
                public GameAppBean load(String key) throws Exception {
                    return getGameAppBean(key);
                }
            });

    /**
     * 根据appId创建该app绑定的第三方的鉴权信息
     *
     * @param appId
     * @param channel
     * @return
     */
    public SealAuth createLiZhiSealAuth(String appId, String channel) {
        GameChannelBean gameChannelBean = getChannelInfoByAppIdAndChannel(appId, channel);
        if (null == gameChannelBean) {
            return null;
        }

        return new SealAuth(gameChannelBean.getAppId(), gameChannelBean.getAppSecret());
    }

    /**
     * 根据AppId 跟 渠道名获取渠道
     *
     * @param appId
     * @param channel
     * @return
     */
    public GameChannelBean getChannelInfoByAppIdAndChannel(String appId, String channel) {
        List<GameChannelBean> gameChannelBeans = getGameChannelsByAppId(appId);
        if (CollectionUtils.isEmpty(gameChannelBeans)) {
            return null;
        }

        for (GameChannelBean bean : gameChannelBeans) {
            if (bean.getChannel().toLowerCase().contains(channel.toLowerCase())) {
                return bean;
            }
        }
        return null;
    }

    /**
     * 根据AppId获取相关联的channel
     *
     * @param appId
     * @return
     */
    public List<GameChannelBean> getGameChannelsByAppId(String appId) {
        GameAppBean bean = getGameAppBean(appId);
        if (null == bean) {
            log.warn("get channel info by appId error.`appId={}", appId);
            return Collections.emptyList();
        }

        List<GameChannelBean> relationChannels = new ArrayList<>();
        List<GameBizGameBean> gameBizGameBeans = bizGameManager.getGameBizGamesByAppId(bean.getId());

        if (!CollectionUtils.isEmpty(gameBizGameBeans)) {
            List<Long> channelIds = gameBizGameBeans.stream().map(x -> x.getChannelId()).distinct().collect(Collectors.toList());
            List<GameChannelBean> gameChannelBeans = gameChannelManager.getAllGameChannels();

            if (!CollectionUtils.isEmpty(gameChannelBeans)) {
                relationChannels = gameChannelBeans.stream().filter(x -> (channelIds.contains(x.getId()))).collect(Collectors.toList());
            }
        }
        return relationChannels;
    }

    public void insertOrUpdateGameApp(long id, String appId, String appSecret, String appName, String appAlias, String appTopic, String operator) {
        boolean insert = id <= 0L;
        Date date = new Date();
        GameAppBean bean = new GameAppBean();
        if (insert) {
            bean.setId(guidGenerator.genId());
            bean.setCreateTime(date);
        } else {
            bean = getGameAppBeanById(id);
            if (null == bean) {
                return;
            }
        }

        bean.setAppId(appId);
        bean.setAppSecret(appSecret);
        bean.setAppName(appName);
        bean.setAppAlias(appAlias);
        bean.setAppTopic(Optional.of(appTopic).orElse(StringUtils.EMPTY));
        bean.setDel(NumberUtils.INTEGER_ZERO);
        bean.setOperator(operator);

        bean.setModifyTime(date);
        if (insert) {
            gameAppBeanMapper.insert(bean);
        } else {
            gameAppBeanMapper.updateByPrimaryKey(bean);
        }
    }

    public void delGameAppById(long id) {
        GameAppBean bean = new GameAppBean();
        bean.setId(id);
        gameAppBeanMapper.deleteByPrimaryKey(bean);
    }

    /**
     * 根据appId获取app 信息
     *
     * @param appId
     * @return
     */
    public GameAppBean getGameAppBean(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }

        GameAppBean gameAppBean = new GameAppBean();
        gameAppBean.setAppId(appId);
        gameAppBean.setDel(NumberUtils.INTEGER_ZERO);
        return gameAppBeanMapper.selectOne(gameAppBean);
    }

    /**
     * 先从本地缓存拿
     */
    public GameAppBean getGameAppBeanCache(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        return gameAppBeanCache.getUnchecked(appId);
    }

    public GameAppBean getGameAppBeanById(long id) {
        GameAppBean gameAppBean = new GameAppBean();
        gameAppBean.setId(id);
        return gameAppBeanMapper.selectByPrimaryKey(gameAppBean);
    }

    public List<GameAppBean> getAllGameApps() {
        GameAppBean gameAppBean = new GameAppBean();
        gameAppBean.setDel(NumberUtils.INTEGER_ZERO);
        return gameAppBeanMapper.selectMany(gameAppBean);
    }

    public PageList<GameAppBean> pageSearchGameApp(long id, String appId, int pageSize, int pageNumber) {
        GameAppBean bean = new GameAppBean();
        if (id > 0L) {
            bean.setId(id);
        }
        if (StringUtils.isNotBlank(appId)) {
            bean.setAppId(appId);
        }

        return gameAppBeanMapper.selectPage(bean, pageNumber, pageSize);
    }
}