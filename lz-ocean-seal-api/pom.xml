<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean</groupId>
        <artifactId>lz-ocean-seal</artifactId>
        <version>1.0.13</version>
    </parent>
    <artifactId>lz-ocean-seal-api</artifactId>
    <name>lz-ocean-seal-api</name>
    <url>http://maven.apache.org</url>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>fm.lizhi.commons</groupId>
                <artifactId>autoapi-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <doclet>fm.lizhi.commons.autoapidoclet.AutoapiDoclet</doclet>
                    <docletArtifact>
                        <groupId>fm.lizhi.commons</groupId>
                        <artifactId>autoapi-doclet</artifactId>
                        <version>0.0.1-SNAPSHOT</version>
                    </docletArtifact>
                    <additionalparam>-groupId ${project.groupId} -artifactId ${project.artifactId} -version ${project.version}
                         -outputDirectory ${project.build.outputDirectory}</additionalparam>
                    <useStandardDocletOptions>false</useStandardDocletOptions>
                    <excludePackageNames>:*.protocol:*.constant</excludePackageNames>
                </configuration>
                <executions>
                    <execution>
                        <id>autoapi-doclet</id>
                        <phase>install</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
