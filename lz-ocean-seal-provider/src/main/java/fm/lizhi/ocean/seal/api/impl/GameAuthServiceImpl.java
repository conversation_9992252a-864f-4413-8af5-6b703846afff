package fm.lizhi.ocean.seal.api.impl;

import com.google.common.base.Strings;
import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.game.auth.enums.SealAuthResultCode;
import fm.lizhi.game.auth.pojo.JwtUserInfo;
import fm.lizhi.game.auth.pojo.ParseResult;
import fm.lizhi.game.auth.pojo.SealAuthCode;
import fm.lizhi.game.auth.pojo.SealAuthToken;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.constant.ChannelType;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.SealRCode;
import fm.lizhi.ocean.seal.dto.AgoraToken;
import fm.lizhi.ocean.seal.gamechannel.SudAuthorService;
import fm.lizhi.ocean.seal.manager.AgoraManager;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.GameUserManager;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import lombok.extern.slf4j.Slf4j;
import tech.sud.mgp.auth.ErrorCodeEnum;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;
import tech.sud.mgp.auth.api.SudSSToken;
import tech.sud.mgp.auth.api.SudUid;

@Slf4j
@ServiceProvider
public class GameAuthServiceImpl implements GameAuthService {

    @Inject
    private SudAuthorService sudAuthorService;
    @Inject
    private GameUserManager gameUserManager;
    @Inject
    private GameAppManager gameAppManager;

    /**
     * 平台SDK与游戏渠道中游戏使用的Token
     *
     * @param userId  用户ID
     * @param appId   appId
     * @param channel 游戏渠道
     * @return
     */
    @Override
    public Result<GameAuthServiceProto.ResponseGetLoginToken> getLoginToken(long userId, String appId, String channel) {
        String logStr = "userId={}`appId={}`channel={}";
        LogContext.addReqLog(logStr, userId, appId, channel);
        LogContext.addResLog(logStr, userId, appId, channel);
        GameAuthServiceProto.ResponseGetLoginToken.Builder resp = GameAuthServiceProto.ResponseGetLoginToken.newBuilder();
        String uid = gameUserManager.getGameUserId(userId, appId);
        if (Strings.isNullOrEmpty(uid)) {
            return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_ERROR, resp.build());
        }
        try {
            if(GameChannel.LIZHI.equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, GameChannel.LIZHI);
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                JwtUserInfo jwtUserInfo = new JwtUserInfo();
                jwtUserInfo.setId(userId);
                SealAuthCode sealAuthCode = sealAuth.getSealAuthCode(jwtUserInfo);
                resp.setToken(GameAuthServiceProto.LoginToken.newBuilder()
                        .setToken(sealAuthCode.getCode())
                        .setExpireDate(sealAuthCode.getExpireDate()).build());
            } else if (GameChannel.SUD.equals(channel)){
                SudMGPAuth sudMGPAuth = sudAuthorService.getSudMGPAuth(appId);
                if (sudMGPAuth == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                SudCode sudCode = sudMGPAuth.getCode(uid);
                resp.setToken(GameAuthServiceProto.LoginToken.newBuilder()
                        .setToken(sudCode.getCode())
                        .setExpireDate(sudCode.getExpireDate()).build());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth.getCode fail, userId:{}, appId:{}, channel:{}, msg:{}", userId, appId, channel, e.getMessage(), e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseGetServerToken> getServerToken(String code, String channel, String appId) {
        String logStr = "loginToken={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, code, channel, appId);
        LogContext.addResLog(logStr, code, channel, appId);
        GameAuthServiceProto.ResponseGetServerToken.Builder resp = GameAuthServiceProto.ResponseGetServerToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ParseResult parseResult = sealAuth.getUserInfoInSealAuthCode(code);
                JwtUserInfo userInfo = parseResult.getUserInfo();
                if(!parseResult.isSuccess() || userInfo == null) {
                    log.warn("parse SealCode fail;parseResult={};loginToken={}", parseResult, code);
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(parseResult.getErrorCode().getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SealAuthToken sealAuthToken = sealAuth.getSealAuthToken(userInfo);
                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(sealAuthToken.getToken()).setExpireDate(sealAuthToken.getExpireDate()).build());
            } else {
                SudMGPAuth sudMGPAuth = sudAuthorService.getSudMGPAuth(appId);
                if (sudMGPAuth == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifyCode(code);
                if (errorCodeEnum != ErrorCodeEnum.SUCCESS) {
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(errorCodeEnum.getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SudUid sudUid = sudMGPAuth.getUidByCode(code);
                if (!sudUid.isSuccess()) {
                    int errorCode = sudUid.getErrorCode();
                    log.warn("getSSToken fail.errorCode={}`loginToken={}", errorCode, code);
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(errorCode).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SudSSToken sudSSToken = sudMGPAuth.getSSToken(sudUid.getUid());
                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(sudSSToken.getToken())
                        .setExpireDate(sudSSToken.getExpireDate())
                        .setUid(sudUid.getUid())
                        .build());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getSSToken fail;code={};channel={}", code, channel, e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseUpdateServerToken> updateServerToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseUpdateServerToken.Builder resp = GameAuthServiceProto.ResponseUpdateServerToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(token);
                JwtUserInfo userInfo = parseResult.getUserInfo();
                if(!parseResult.isSuccess() || userInfo == null) {
                    log.warn("parse SealCode fail;parseResult={};sealResourceToken={}", parseResult, token);
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(parseResult.getErrorCode().getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SealAuthToken sealAuthToken = sealAuth.getSealAuthToken(userInfo);
                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(sealAuthToken.getToken()).setExpireDate(sealAuthToken.getExpireDate()).build());
            } else {
                SudMGPAuth sudMGPAuth = sudAuthorService.getSudMGPAuth(appId);
                if (sudMGPAuth == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifySSToken(token);
                if (errorCodeEnum != ErrorCodeEnum.SUCCESS) {
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(errorCodeEnum.getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SudUid sudUid = sudMGPAuth.getUidBySSToken(token);
                if (!sudUid.isSuccess()) {
                    int errorCode = sudUid.getErrorCode();
                    log.warn("updateSSToken fail.errorCode={}`ssToken={}", errorCode, token);
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(errorCode).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SudSSToken sudSSToken = sudMGPAuth.getSSToken(sudUid.getUid());
                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(sudSSToken.getToken()).setExpireDate(sudSSToken.getExpireDate()).build());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth updateSSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseGetUserByServerToken> getUserByServerToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseGetUserByServerToken.Builder resp = GameAuthServiceProto.ResponseGetUserByServerToken.newBuilder();
        long userIdByGameUserId;
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(token);
                JwtUserInfo userInfo = parseResult.getUserInfo();
                if(!parseResult.isSuccess() || userInfo == null) {
                    log.warn("parse SealCode fail;parseResult={};sealResourceToken={}", parseResult, token);
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                userIdByGameUserId = userInfo.getId();
            } else {
                SudMGPAuth sudMGPAuth = sudAuthorService.getSudMGPAuth(appId);
                if (sudMGPAuth == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifySSToken(token);
                if (errorCodeEnum != ErrorCodeEnum.SUCCESS) {
                    resp.setGameUser(GameAuthServiceProto.GameUser.newBuilder().setErrorCode(errorCodeEnum.getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SudUid sudUid = sudMGPAuth.getUidBySSToken(token);
                if (!sudUid.isSuccess()) {
                    int errorCode = sudUid.getErrorCode();
                    log.warn("updateSSToken fail.errorCode={}`ssToken={}", errorCode, token);
                    resp.setGameUser(GameAuthServiceProto.GameUser.newBuilder().setErrorCode(errorCode).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                userIdByGameUserId = gameUserManager.converUid2SealUserId(sudUid.getUid());
            }
            if (userIdByGameUserId == 0L) {
                return new Result<>(SealRCode.SEAL_RCODE_UNKNOWN_ERROR, resp.build());
            }
            resp.setGameUser(GameAuthServiceProto.GameUser.newBuilder()
                    .setGameUserId(String.valueOf(userIdByGameUserId)).setUserId(userIdByGameUserId).setAppId(appId).build());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseVerifyLoginToken> verifyLoginToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseVerifyLoginToken.Builder resp = GameAuthServiceProto.ResponseVerifyLoginToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                SealAuthResultCode resultCode = sealAuth.verifySealAuthCode(token);
                resp.setErrorCode(resultCode.getCode());
            } else {
                SudMGPAuth sudMGPAuth = sudAuthorService.getSudMGPAuth(appId);
                if (sudMGPAuth == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifyCode(token);
                resp.setErrorCode(errorCodeEnum.getCode());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseVerifyServerToken> verifyServerToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseVerifyServerToken.Builder resp = GameAuthServiceProto.ResponseVerifyServerToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                SealAuthResultCode resultCode = sealAuth.verifySealAuthCode(token);
                resp.setErrorCode(resultCode.getCode());
            } else {
                SudMGPAuth sudMGPAuth = sudAuthorService.getSudMGPAuth(appId);
                if (sudMGPAuth == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifySSToken(token);
                resp.setErrorCode(errorCodeEnum.getCode());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }
}
