// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameServiceProto {
  private GameServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GameDetailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 gameId = 1;
    /**
     * <code>required int64 gameId = 1;</code>
     *
     * <pre>
     * 平台游戏ID
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required int64 gameId = 1;</code>
     *
     * <pre>
     * 平台游戏ID
     * </pre>
     */
    long getGameId();

    // required string renderType = 2;
    /**
     * <code>required string renderType = 2;</code>
     *
     * <pre>
     * 渲染类型，具体看：RenderType
     * </pre>
     */
    boolean hasRenderType();
    /**
     * <code>required string renderType = 2;</code>
     *
     * <pre>
     * 渲染类型，具体看：RenderType
     * </pre>
     */
    java.lang.String getRenderType();
    /**
     * <code>required string renderType = 2;</code>
     *
     * <pre>
     * 渲染类型，具体看：RenderType
     * </pre>
     */
    com.google.protobuf.ByteString
        getRenderTypeBytes();

    // required string channelGameId = 3;
    /**
     * <code>required string channelGameId = 3;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    boolean hasChannelGameId();
    /**
     * <code>required string channelGameId = 3;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    java.lang.String getChannelGameId();
    /**
     * <code>required string channelGameId = 3;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelGameIdBytes();

    // required string channel = 4;
    /**
     * <code>required string channel = 4;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>required string channel = 4;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>required string channel = 4;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // required string config = 5;
    /**
     * <code>required string config = 5;</code>
     *
     * <pre>
     * 配置信息，JSON结构
     * </pre>
     */
    boolean hasConfig();
    /**
     * <code>required string config = 5;</code>
     *
     * <pre>
     * 配置信息，JSON结构
     * </pre>
     */
    java.lang.String getConfig();
    /**
     * <code>required string config = 5;</code>
     *
     * <pre>
     * 配置信息，JSON结构
     * </pre>
     */
    com.google.protobuf.ByteString
        getConfigBytes();

    // required string gameName = 6;
    /**
     * <code>required string gameName = 6;</code>
     *
     * <pre>
     * 游戏名称
     * </pre>
     */
    boolean hasGameName();
    /**
     * <code>required string gameName = 6;</code>
     *
     * <pre>
     * 游戏名称
     * </pre>
     */
    java.lang.String getGameName();
    /**
     * <code>required string gameName = 6;</code>
     *
     * <pre>
     * 游戏名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameNameBytes();

    // required int32 captain = 7;
    /**
     * <code>required int32 captain = 7;</code>
     *
     * <pre>
     * 是否需要队长
     * </pre>
     */
    boolean hasCaptain();
    /**
     * <code>required int32 captain = 7;</code>
     *
     * <pre>
     * 是否需要队长
     * </pre>
     */
    int getCaptain();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameDetail}
   *
   * <pre>
   ** 游戏详情 
   * </pre>
   */
  public static final class GameDetail extends
      com.google.protobuf.GeneratedMessage
      implements GameDetailOrBuilder {
    // Use GameDetail.newBuilder() to construct.
    private GameDetail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameDetail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameDetail defaultInstance;
    public static GameDetail getDefaultInstance() {
      return defaultInstance;
    }

    public GameDetail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameDetail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              gameId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              renderType_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channelGameId_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              channel_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              config_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              gameName_ = input.readBytes();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              captain_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder.class);
    }

    public static com.google.protobuf.Parser<GameDetail> PARSER =
        new com.google.protobuf.AbstractParser<GameDetail>() {
      public GameDetail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameDetail(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameDetail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 gameId = 1;
    public static final int GAMEID_FIELD_NUMBER = 1;
    private long gameId_;
    /**
     * <code>required int64 gameId = 1;</code>
     *
     * <pre>
     * 平台游戏ID
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 gameId = 1;</code>
     *
     * <pre>
     * 平台游戏ID
     * </pre>
     */
    public long getGameId() {
      return gameId_;
    }

    // required string renderType = 2;
    public static final int RENDERTYPE_FIELD_NUMBER = 2;
    private java.lang.Object renderType_;
    /**
     * <code>required string renderType = 2;</code>
     *
     * <pre>
     * 渲染类型，具体看：RenderType
     * </pre>
     */
    public boolean hasRenderType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string renderType = 2;</code>
     *
     * <pre>
     * 渲染类型，具体看：RenderType
     * </pre>
     */
    public java.lang.String getRenderType() {
      java.lang.Object ref = renderType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          renderType_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string renderType = 2;</code>
     *
     * <pre>
     * 渲染类型，具体看：RenderType
     * </pre>
     */
    public com.google.protobuf.ByteString
        getRenderTypeBytes() {
      java.lang.Object ref = renderType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        renderType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string channelGameId = 3;
    public static final int CHANNELGAMEID_FIELD_NUMBER = 3;
    private java.lang.Object channelGameId_;
    /**
     * <code>required string channelGameId = 3;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public boolean hasChannelGameId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string channelGameId = 3;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public java.lang.String getChannelGameId() {
      java.lang.Object ref = channelGameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelGameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channelGameId = 3;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelGameIdBytes() {
      java.lang.Object ref = channelGameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelGameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string channel = 4;
    public static final int CHANNEL_FIELD_NUMBER = 4;
    private java.lang.Object channel_;
    /**
     * <code>required string channel = 4;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string channel = 4;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channel = 4;</code>
     *
     * <pre>
     * 渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string config = 5;
    public static final int CONFIG_FIELD_NUMBER = 5;
    private java.lang.Object config_;
    /**
     * <code>required string config = 5;</code>
     *
     * <pre>
     * 配置信息，JSON结构
     * </pre>
     */
    public boolean hasConfig() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string config = 5;</code>
     *
     * <pre>
     * 配置信息，JSON结构
     * </pre>
     */
    public java.lang.String getConfig() {
      java.lang.Object ref = config_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          config_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string config = 5;</code>
     *
     * <pre>
     * 配置信息，JSON结构
     * </pre>
     */
    public com.google.protobuf.ByteString
        getConfigBytes() {
      java.lang.Object ref = config_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        config_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string gameName = 6;
    public static final int GAMENAME_FIELD_NUMBER = 6;
    private java.lang.Object gameName_;
    /**
     * <code>required string gameName = 6;</code>
     *
     * <pre>
     * 游戏名称
     * </pre>
     */
    public boolean hasGameName() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required string gameName = 6;</code>
     *
     * <pre>
     * 游戏名称
     * </pre>
     */
    public java.lang.String getGameName() {
      java.lang.Object ref = gameName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameName = 6;</code>
     *
     * <pre>
     * 游戏名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameNameBytes() {
      java.lang.Object ref = gameName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 captain = 7;
    public static final int CAPTAIN_FIELD_NUMBER = 7;
    private int captain_;
    /**
     * <code>required int32 captain = 7;</code>
     *
     * <pre>
     * 是否需要队长
     * </pre>
     */
    public boolean hasCaptain() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required int32 captain = 7;</code>
     *
     * <pre>
     * 是否需要队长
     * </pre>
     */
    public int getCaptain() {
      return captain_;
    }

    private void initFields() {
      gameId_ = 0L;
      renderType_ = "";
      channelGameId_ = "";
      channel_ = "";
      config_ = "";
      gameName_ = "";
      captain_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRenderType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChannelGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChannel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasConfig()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCaptain()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, gameId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getRenderTypeBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelGameIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getChannelBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getConfigBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getGameNameBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, captain_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, gameId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getRenderTypeBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelGameIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getChannelBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getConfigBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getGameNameBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, captain_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameDetail}
     *
     * <pre>
     ** 游戏详情 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        gameId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        renderType_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        channelGameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        config_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        gameName_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        captain_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.renderType_ = renderType_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelGameId_ = channelGameId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.config_ = config_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.gameName_ = gameName_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.captain_ = captain_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance()) return this;
        if (other.hasGameId()) {
          setGameId(other.getGameId());
        }
        if (other.hasRenderType()) {
          bitField0_ |= 0x00000002;
          renderType_ = other.renderType_;
          onChanged();
        }
        if (other.hasChannelGameId()) {
          bitField0_ |= 0x00000004;
          channelGameId_ = other.channelGameId_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000008;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasConfig()) {
          bitField0_ |= 0x00000010;
          config_ = other.config_;
          onChanged();
        }
        if (other.hasGameName()) {
          bitField0_ |= 0x00000020;
          gameName_ = other.gameName_;
          onChanged();
        }
        if (other.hasCaptain()) {
          setCaptain(other.getCaptain());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGameId()) {
          
          return false;
        }
        if (!hasRenderType()) {
          
          return false;
        }
        if (!hasChannelGameId()) {
          
          return false;
        }
        if (!hasChannel()) {
          
          return false;
        }
        if (!hasConfig()) {
          
          return false;
        }
        if (!hasGameName()) {
          
          return false;
        }
        if (!hasCaptain()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 gameId = 1;
      private long gameId_ ;
      /**
       * <code>required int64 gameId = 1;</code>
       *
       * <pre>
       * 平台游戏ID
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 gameId = 1;</code>
       *
       * <pre>
       * 平台游戏ID
       * </pre>
       */
      public long getGameId() {
        return gameId_;
      }
      /**
       * <code>required int64 gameId = 1;</code>
       *
       * <pre>
       * 平台游戏ID
       * </pre>
       */
      public Builder setGameId(long value) {
        bitField0_ |= 0x00000001;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 gameId = 1;</code>
       *
       * <pre>
       * 平台游戏ID
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = 0L;
        onChanged();
        return this;
      }

      // required string renderType = 2;
      private java.lang.Object renderType_ = "";
      /**
       * <code>required string renderType = 2;</code>
       *
       * <pre>
       * 渲染类型，具体看：RenderType
       * </pre>
       */
      public boolean hasRenderType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string renderType = 2;</code>
       *
       * <pre>
       * 渲染类型，具体看：RenderType
       * </pre>
       */
      public java.lang.String getRenderType() {
        java.lang.Object ref = renderType_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          renderType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string renderType = 2;</code>
       *
       * <pre>
       * 渲染类型，具体看：RenderType
       * </pre>
       */
      public com.google.protobuf.ByteString
          getRenderTypeBytes() {
        java.lang.Object ref = renderType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          renderType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string renderType = 2;</code>
       *
       * <pre>
       * 渲染类型，具体看：RenderType
       * </pre>
       */
      public Builder setRenderType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        renderType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string renderType = 2;</code>
       *
       * <pre>
       * 渲染类型，具体看：RenderType
       * </pre>
       */
      public Builder clearRenderType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        renderType_ = getDefaultInstance().getRenderType();
        onChanged();
        return this;
      }
      /**
       * <code>required string renderType = 2;</code>
       *
       * <pre>
       * 渲染类型，具体看：RenderType
       * </pre>
       */
      public Builder setRenderTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        renderType_ = value;
        onChanged();
        return this;
      }

      // required string channelGameId = 3;
      private java.lang.Object channelGameId_ = "";
      /**
       * <code>required string channelGameId = 3;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public boolean hasChannelGameId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string channelGameId = 3;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public java.lang.String getChannelGameId() {
        java.lang.Object ref = channelGameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelGameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channelGameId = 3;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelGameIdBytes() {
        java.lang.Object ref = channelGameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelGameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channelGameId = 3;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder setChannelGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelGameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channelGameId = 3;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder clearChannelGameId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelGameId_ = getDefaultInstance().getChannelGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string channelGameId = 3;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder setChannelGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelGameId_ = value;
        onChanged();
        return this;
      }

      // required string channel = 4;
      private java.lang.Object channel_ = "";
      /**
       * <code>required string channel = 4;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string channel = 4;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channel = 4;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channel = 4;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channel = 4;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000008);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>required string channel = 4;</code>
       *
       * <pre>
       * 渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        channel_ = value;
        onChanged();
        return this;
      }

      // required string config = 5;
      private java.lang.Object config_ = "";
      /**
       * <code>required string config = 5;</code>
       *
       * <pre>
       * 配置信息，JSON结构
       * </pre>
       */
      public boolean hasConfig() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string config = 5;</code>
       *
       * <pre>
       * 配置信息，JSON结构
       * </pre>
       */
      public java.lang.String getConfig() {
        java.lang.Object ref = config_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          config_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string config = 5;</code>
       *
       * <pre>
       * 配置信息，JSON结构
       * </pre>
       */
      public com.google.protobuf.ByteString
          getConfigBytes() {
        java.lang.Object ref = config_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          config_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string config = 5;</code>
       *
       * <pre>
       * 配置信息，JSON结构
       * </pre>
       */
      public Builder setConfig(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        config_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string config = 5;</code>
       *
       * <pre>
       * 配置信息，JSON结构
       * </pre>
       */
      public Builder clearConfig() {
        bitField0_ = (bitField0_ & ~0x00000010);
        config_ = getDefaultInstance().getConfig();
        onChanged();
        return this;
      }
      /**
       * <code>required string config = 5;</code>
       *
       * <pre>
       * 配置信息，JSON结构
       * </pre>
       */
      public Builder setConfigBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        config_ = value;
        onChanged();
        return this;
      }

      // required string gameName = 6;
      private java.lang.Object gameName_ = "";
      /**
       * <code>required string gameName = 6;</code>
       *
       * <pre>
       * 游戏名称
       * </pre>
       */
      public boolean hasGameName() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required string gameName = 6;</code>
       *
       * <pre>
       * 游戏名称
       * </pre>
       */
      public java.lang.String getGameName() {
        java.lang.Object ref = gameName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameName = 6;</code>
       *
       * <pre>
       * 游戏名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameNameBytes() {
        java.lang.Object ref = gameName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameName = 6;</code>
       *
       * <pre>
       * 游戏名称
       * </pre>
       */
      public Builder setGameName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        gameName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameName = 6;</code>
       *
       * <pre>
       * 游戏名称
       * </pre>
       */
      public Builder clearGameName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        gameName_ = getDefaultInstance().getGameName();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameName = 6;</code>
       *
       * <pre>
       * 游戏名称
       * </pre>
       */
      public Builder setGameNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        gameName_ = value;
        onChanged();
        return this;
      }

      // required int32 captain = 7;
      private int captain_ ;
      /**
       * <code>required int32 captain = 7;</code>
       *
       * <pre>
       * 是否需要队长
       * </pre>
       */
      public boolean hasCaptain() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required int32 captain = 7;</code>
       *
       * <pre>
       * 是否需要队长
       * </pre>
       */
      public int getCaptain() {
        return captain_;
      }
      /**
       * <code>required int32 captain = 7;</code>
       *
       * <pre>
       * 是否需要队长
       * </pre>
       */
      public Builder setCaptain(int value) {
        bitField0_ |= 0x00000040;
        captain_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 captain = 7;</code>
       *
       * <pre>
       * 是否需要队长
       * </pre>
       */
      public Builder clearCaptain() {
        bitField0_ = (bitField0_ & ~0x00000040);
        captain_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameDetail)
    }

    static {
      defaultInstance = new GameDetail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameDetail)
  }

  public interface GetGameDetailParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required string gameId = 2;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam}
   *
   * <pre>
   ** 获取游戏详情参数 
   * </pre>
   */
  public static final class GetGameDetailParam extends
      com.google.protobuf.GeneratedMessage
      implements GetGameDetailParamOrBuilder {
    // Use GetGameDetailParam.newBuilder() to construct.
    private GetGameDetailParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetGameDetailParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetGameDetailParam defaultInstance;
    public static GetGameDetailParam getDefaultInstance() {
      return defaultInstance;
    }

    public GetGameDetailParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetGameDetailParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GetGameDetailParam> PARSER =
        new com.google.protobuf.AbstractParser<GetGameDetailParam>() {
      public GetGameDetailParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetGameDetailParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetGameDetailParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string gameId = 2;
    public static final int GAMEID_FIELD_NUMBER = 2;
    private java.lang.Object gameId_;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
      gameId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam}
     *
     * <pre>
     ** 获取游戏详情参数 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000002;
          gameId_ = other.gameId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // required string gameId = 2;
      private java.lang.Object gameId_ = "";
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam)
    }

    static {
      defaultInstance = new GetGameDetailParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam)
  }

  public interface RequestGetGameDetailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGameDetail}
   *
   * <pre>
   * GameService.java
   * 获取游戏详情
   * domain = 4302, op = 61
   * </pre>
   */
  public static final class RequestGetGameDetail extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetGameDetailOrBuilder {
    // Use RequestGetGameDetail.newBuilder() to construct.
    private RequestGetGameDetail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetGameDetail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetGameDetail defaultInstance;
    public static RequestGetGameDetail getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetGameDetail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetGameDetail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetGameDetail> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetGameDetail>() {
      public RequestGetGameDetail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetGameDetail(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetGameDetail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGameDetail}
     *
     * <pre>
     * GameService.java
     * 获取游戏详情
     * domain = 4302, op = 61
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetGameDetailParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGameDetail)
    }

    static {
      defaultInstance = new RequestGetGameDetail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGameDetail)
  }

  public interface ResponseGetGameDetailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
     */
    boolean hasGameDetail();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail getGameDetail();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder getGameDetailOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameDetail}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetGameDetail extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetGameDetailOrBuilder {
    // Use ResponseGetGameDetail.newBuilder() to construct.
    private ResponseGetGameDetail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetGameDetail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetGameDetail defaultInstance;
    public static ResponseGetGameDetail getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetGameDetail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetGameDetail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = gameDetail_.toBuilder();
              }
              gameDetail_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(gameDetail_);
                gameDetail_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetGameDetail> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetGameDetail>() {
      public ResponseGetGameDetail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetGameDetail(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetGameDetail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;
    public static final int GAMEDETAIL_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail gameDetail_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
     */
    public boolean hasGameDetail() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail getGameDetail() {
      return gameDetail_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder getGameDetailOrBuilder() {
      return gameDetail_;
    }

    private void initFields() {
      gameDetail_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasGameDetail()) {
        if (!getGameDetail().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, gameDetail_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameDetail_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameDetail}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameDetailFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameDetailBuilder_ == null) {
          gameDetail_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance();
        } else {
          gameDetailBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (gameDetailBuilder_ == null) {
          result.gameDetail_ = gameDetail_;
        } else {
          result.gameDetail_ = gameDetailBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail.getDefaultInstance()) return this;
        if (other.hasGameDetail()) {
          mergeGameDetail(other.getGameDetail());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasGameDetail()) {
          if (!getGameDetail().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;
      private fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail gameDetail_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder> gameDetailBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public boolean hasGameDetail() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail getGameDetail() {
        if (gameDetailBuilder_ == null) {
          return gameDetail_;
        } else {
          return gameDetailBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public Builder setGameDetail(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail value) {
        if (gameDetailBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gameDetail_ = value;
          onChanged();
        } else {
          gameDetailBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public Builder setGameDetail(
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder builderForValue) {
        if (gameDetailBuilder_ == null) {
          gameDetail_ = builderForValue.build();
          onChanged();
        } else {
          gameDetailBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public Builder mergeGameDetail(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail value) {
        if (gameDetailBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              gameDetail_ != fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance()) {
            gameDetail_ =
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.newBuilder(gameDetail_).mergeFrom(value).buildPartial();
          } else {
            gameDetail_ = value;
          }
          onChanged();
        } else {
          gameDetailBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public Builder clearGameDetail() {
        if (gameDetailBuilder_ == null) {
          gameDetail_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.getDefaultInstance();
          onChanged();
        } else {
          gameDetailBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder getGameDetailBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getGameDetailFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder getGameDetailOrBuilder() {
        if (gameDetailBuilder_ != null) {
          return gameDetailBuilder_.getMessageOrBuilder();
        } else {
          return gameDetail_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameDetail gameDetail = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder> 
          getGameDetailFieldBuilder() {
        if (gameDetailBuilder_ == null) {
          gameDetailBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetailOrBuilder>(
                  gameDetail_,
                  getParentForChildren(),
                  isClean());
          gameDetail_ = null;
        }
        return gameDetailBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameDetail)
    }

    static {
      defaultInstance = new ResponseGetGameDetail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameDetail)
  }

  public interface UpdateGameConfigParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required string gameId = 2;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();

    // required string config = 3;
    /**
     * <code>required string config = 3;</code>
     *
     * <pre>
     * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
     * </pre>
     */
    boolean hasConfig();
    /**
     * <code>required string config = 3;</code>
     *
     * <pre>
     * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
     * </pre>
     */
    java.lang.String getConfig();
    /**
     * <code>required string config = 3;</code>
     *
     * <pre>
     * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
     * </pre>
     */
    com.google.protobuf.ByteString
        getConfigBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam}
   *
   * <pre>
   ** 更新游戏配置信息参数 
   * </pre>
   */
  public static final class UpdateGameConfigParam extends
      com.google.protobuf.GeneratedMessage
      implements UpdateGameConfigParamOrBuilder {
    // Use UpdateGameConfigParam.newBuilder() to construct.
    private UpdateGameConfigParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private UpdateGameConfigParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final UpdateGameConfigParam defaultInstance;
    public static UpdateGameConfigParam getDefaultInstance() {
      return defaultInstance;
    }

    public UpdateGameConfigParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private UpdateGameConfigParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              config_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder.class);
    }

    public static com.google.protobuf.Parser<UpdateGameConfigParam> PARSER =
        new com.google.protobuf.AbstractParser<UpdateGameConfigParam>() {
      public UpdateGameConfigParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdateGameConfigParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateGameConfigParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string gameId = 2;
    public static final int GAMEID_FIELD_NUMBER = 2;
    private java.lang.Object gameId_;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string config = 3;
    public static final int CONFIG_FIELD_NUMBER = 3;
    private java.lang.Object config_;
    /**
     * <code>required string config = 3;</code>
     *
     * <pre>
     * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
     * </pre>
     */
    public boolean hasConfig() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string config = 3;</code>
     *
     * <pre>
     * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
     * </pre>
     */
    public java.lang.String getConfig() {
      java.lang.Object ref = config_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          config_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string config = 3;</code>
     *
     * <pre>
     * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
     * </pre>
     */
    public com.google.protobuf.ByteString
        getConfigBytes() {
      java.lang.Object ref = config_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        config_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
      gameId_ = "";
      config_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasConfig()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getConfigBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getConfigBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam}
     *
     * <pre>
     ** 更新游戏配置信息参数 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        config_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.config_ = config_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000002;
          gameId_ = other.gameId_;
          onChanged();
        }
        if (other.hasConfig()) {
          bitField0_ |= 0x00000004;
          config_ = other.config_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        if (!hasConfig()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // required string gameId = 2;
      private java.lang.Object gameId_ = "";
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }

      // required string config = 3;
      private java.lang.Object config_ = "";
      /**
       * <code>required string config = 3;</code>
       *
       * <pre>
       * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
       * </pre>
       */
      public boolean hasConfig() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string config = 3;</code>
       *
       * <pre>
       * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
       * </pre>
       */
      public java.lang.String getConfig() {
        java.lang.Object ref = config_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          config_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string config = 3;</code>
       *
       * <pre>
       * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
       * </pre>
       */
      public com.google.protobuf.ByteString
          getConfigBytes() {
        java.lang.Object ref = config_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          config_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string config = 3;</code>
       *
       * <pre>
       * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
       * </pre>
       */
      public Builder setConfig(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        config_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string config = 3;</code>
       *
       * <pre>
       * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
       * </pre>
       */
      public Builder clearConfig() {
        bitField0_ = (bitField0_ & ~0x00000004);
        config_ = getDefaultInstance().getConfig();
        onChanged();
        return this;
      }
      /**
       * <code>required string config = 3;</code>
       *
       * <pre>
       * 配置格式：{"minPlayer": 2, "maxPlayer": 4}
       * </pre>
       */
      public Builder setConfigBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        config_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam)
    }

    static {
      defaultInstance = new UpdateGameConfigParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam)
  }

  public interface RequestUpdateGameConfigOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestUpdateGameConfig}
   *
   * <pre>
   * GameService.java
   * 更新游戏配置信息
   * domain = 4302, op = 62
   * </pre>
   */
  public static final class RequestUpdateGameConfig extends
      com.google.protobuf.GeneratedMessage
      implements RequestUpdateGameConfigOrBuilder {
    // Use RequestUpdateGameConfig.newBuilder() to construct.
    private RequestUpdateGameConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestUpdateGameConfig(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestUpdateGameConfig defaultInstance;
    public static RequestUpdateGameConfig getDefaultInstance() {
      return defaultInstance;
    }

    public RequestUpdateGameConfig getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestUpdateGameConfig(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestUpdateGameConfig> PARSER =
        new com.google.protobuf.AbstractParser<RequestUpdateGameConfig>() {
      public RequestUpdateGameConfig parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestUpdateGameConfig(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestUpdateGameConfig> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestUpdateGameConfig}
     *
     * <pre>
     * GameService.java
     * 更新游戏配置信息
     * domain = 4302, op = 62
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfigOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.UpdateGameConfigParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestUpdateGameConfig)
    }

    static {
      defaultInstance = new RequestUpdateGameConfig(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestUpdateGameConfig)
  }

  public interface ResponseUpdateGameConfigOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateGameConfig}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseUpdateGameConfig extends
      com.google.protobuf.GeneratedMessage
      implements ResponseUpdateGameConfigOrBuilder {
    // Use ResponseUpdateGameConfig.newBuilder() to construct.
    private ResponseUpdateGameConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseUpdateGameConfig(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseUpdateGameConfig defaultInstance;
    public static ResponseUpdateGameConfig getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseUpdateGameConfig getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseUpdateGameConfig(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseUpdateGameConfig> PARSER =
        new com.google.protobuf.AbstractParser<ResponseUpdateGameConfig>() {
      public ResponseUpdateGameConfig parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseUpdateGameConfig(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseUpdateGameConfig> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateGameConfig}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfigOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateGameConfig)
    }

    static {
      defaultInstance = new ResponseUpdateGameConfig(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateGameConfig)
  }

  public interface GameVersionParamsOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 gameVersionCode = 1;
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     * 游戏版本号，Builder号
     * </pre>
     */
    boolean hasGameVersionCode();
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     * 游戏版本号，Builder号
     * </pre>
     */
    long getGameVersionCode();

    // required string gameId = 2;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameVersionParams}
   */
  public static final class GameVersionParams extends
      com.google.protobuf.GeneratedMessage
      implements GameVersionParamsOrBuilder {
    // Use GameVersionParams.newBuilder() to construct.
    private GameVersionParams(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameVersionParams(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameVersionParams defaultInstance;
    public static GameVersionParams getDefaultInstance() {
      return defaultInstance;
    }

    public GameVersionParams getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameVersionParams(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              gameVersionCode_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder.class);
    }

    public static com.google.protobuf.Parser<GameVersionParams> PARSER =
        new com.google.protobuf.AbstractParser<GameVersionParams>() {
      public GameVersionParams parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameVersionParams(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameVersionParams> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 gameVersionCode = 1;
    public static final int GAMEVERSIONCODE_FIELD_NUMBER = 1;
    private long gameVersionCode_;
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     * 游戏版本号，Builder号
     * </pre>
     */
    public boolean hasGameVersionCode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     * 游戏版本号，Builder号
     * </pre>
     */
    public long getGameVersionCode() {
      return gameVersionCode_;
    }

    // required string gameId = 2;
    public static final int GAMEID_FIELD_NUMBER = 2;
    private java.lang.Object gameId_;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      gameVersionCode_ = 0L;
      gameId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGameVersionCode()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, gameVersionCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, gameVersionCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameVersionParams}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        gameVersionCode_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.gameVersionCode_ = gameVersionCode_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.getDefaultInstance()) return this;
        if (other.hasGameVersionCode()) {
          setGameVersionCode(other.getGameVersionCode());
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000002;
          gameId_ = other.gameId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGameVersionCode()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 gameVersionCode = 1;
      private long gameVersionCode_ ;
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       * 游戏版本号，Builder号
       * </pre>
       */
      public boolean hasGameVersionCode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       * 游戏版本号，Builder号
       * </pre>
       */
      public long getGameVersionCode() {
        return gameVersionCode_;
      }
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       * 游戏版本号，Builder号
       * </pre>
       */
      public Builder setGameVersionCode(long value) {
        bitField0_ |= 0x00000001;
        gameVersionCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       * 游戏版本号，Builder号
       * </pre>
       */
      public Builder clearGameVersionCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        gameVersionCode_ = 0L;
        onChanged();
        return this;
      }

      // required string gameId = 2;
      private java.lang.Object gameId_ = "";
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameVersionParams)
    }

    static {
      defaultInstance = new GameVersionParams(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameVersionParams)
  }

  public interface GameVersionInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 gameVersionCode = 1;
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     ** 游戏版本号，Builder号 
     * </pre>
     */
    boolean hasGameVersionCode();
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     ** 游戏版本号，Builder号 
     * </pre>
     */
    long getGameVersionCode();

    // required int64 gameId = 3;
    /**
     * <code>required int64 gameId = 3;</code>
     *
     * <pre>
     ** 平台游戏ID 
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required int64 gameId = 3;</code>
     *
     * <pre>
     ** 平台游戏ID 
     * </pre>
     */
    long getGameId();

    // required string channelGameId = 4;
    /**
     * <code>required string channelGameId = 4;</code>
     *
     * <pre>
     ** 渠道游戏ID 
     * </pre>
     */
    boolean hasChannelGameId();
    /**
     * <code>required string channelGameId = 4;</code>
     *
     * <pre>
     ** 渠道游戏ID 
     * </pre>
     */
    java.lang.String getChannelGameId();
    /**
     * <code>required string channelGameId = 4;</code>
     *
     * <pre>
     ** 渠道游戏ID 
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelGameIdBytes();

    // required bool forceUpdate = 5;
    /**
     * <code>required bool forceUpdate = 5;</code>
     *
     * <pre>
     ** 是否强制更新 
     * </pre>
     */
    boolean hasForceUpdate();
    /**
     * <code>required bool forceUpdate = 5;</code>
     *
     * <pre>
     ** 是否强制更新 
     * </pre>
     */
    boolean getForceUpdate();

    // required string downloadLink = 6;
    /**
     * <code>required string downloadLink = 6;</code>
     *
     * <pre>
     ** 下载链接 
     * </pre>
     */
    boolean hasDownloadLink();
    /**
     * <code>required string downloadLink = 6;</code>
     *
     * <pre>
     ** 下载链接 
     * </pre>
     */
    java.lang.String getDownloadLink();
    /**
     * <code>required string downloadLink = 6;</code>
     *
     * <pre>
     ** 下载链接 
     * </pre>
     */
    com.google.protobuf.ByteString
        getDownloadLinkBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo}
   */
  public static final class GameVersionInfo extends
      com.google.protobuf.GeneratedMessage
      implements GameVersionInfoOrBuilder {
    // Use GameVersionInfo.newBuilder() to construct.
    private GameVersionInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameVersionInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameVersionInfo defaultInstance;
    public static GameVersionInfo getDefaultInstance() {
      return defaultInstance;
    }

    public GameVersionInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameVersionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              gameVersionCode_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readInt64();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000004;
              channelGameId_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              forceUpdate_ = input.readBool();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000010;
              downloadLink_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<GameVersionInfo> PARSER =
        new com.google.protobuf.AbstractParser<GameVersionInfo>() {
      public GameVersionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameVersionInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameVersionInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 gameVersionCode = 1;
    public static final int GAMEVERSIONCODE_FIELD_NUMBER = 1;
    private long gameVersionCode_;
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     ** 游戏版本号，Builder号 
     * </pre>
     */
    public boolean hasGameVersionCode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 gameVersionCode = 1;</code>
     *
     * <pre>
     ** 游戏版本号，Builder号 
     * </pre>
     */
    public long getGameVersionCode() {
      return gameVersionCode_;
    }

    // required int64 gameId = 3;
    public static final int GAMEID_FIELD_NUMBER = 3;
    private long gameId_;
    /**
     * <code>required int64 gameId = 3;</code>
     *
     * <pre>
     ** 平台游戏ID 
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int64 gameId = 3;</code>
     *
     * <pre>
     ** 平台游戏ID 
     * </pre>
     */
    public long getGameId() {
      return gameId_;
    }

    // required string channelGameId = 4;
    public static final int CHANNELGAMEID_FIELD_NUMBER = 4;
    private java.lang.Object channelGameId_;
    /**
     * <code>required string channelGameId = 4;</code>
     *
     * <pre>
     ** 渠道游戏ID 
     * </pre>
     */
    public boolean hasChannelGameId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string channelGameId = 4;</code>
     *
     * <pre>
     ** 渠道游戏ID 
     * </pre>
     */
    public java.lang.String getChannelGameId() {
      java.lang.Object ref = channelGameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelGameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channelGameId = 4;</code>
     *
     * <pre>
     ** 渠道游戏ID 
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelGameIdBytes() {
      java.lang.Object ref = channelGameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelGameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required bool forceUpdate = 5;
    public static final int FORCEUPDATE_FIELD_NUMBER = 5;
    private boolean forceUpdate_;
    /**
     * <code>required bool forceUpdate = 5;</code>
     *
     * <pre>
     ** 是否强制更新 
     * </pre>
     */
    public boolean hasForceUpdate() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required bool forceUpdate = 5;</code>
     *
     * <pre>
     ** 是否强制更新 
     * </pre>
     */
    public boolean getForceUpdate() {
      return forceUpdate_;
    }

    // required string downloadLink = 6;
    public static final int DOWNLOADLINK_FIELD_NUMBER = 6;
    private java.lang.Object downloadLink_;
    /**
     * <code>required string downloadLink = 6;</code>
     *
     * <pre>
     ** 下载链接 
     * </pre>
     */
    public boolean hasDownloadLink() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string downloadLink = 6;</code>
     *
     * <pre>
     ** 下载链接 
     * </pre>
     */
    public java.lang.String getDownloadLink() {
      java.lang.Object ref = downloadLink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          downloadLink_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string downloadLink = 6;</code>
     *
     * <pre>
     ** 下载链接 
     * </pre>
     */
    public com.google.protobuf.ByteString
        getDownloadLinkBytes() {
      java.lang.Object ref = downloadLink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        downloadLink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      gameVersionCode_ = 0L;
      gameId_ = 0L;
      channelGameId_ = "";
      forceUpdate_ = false;
      downloadLink_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGameVersionCode()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChannelGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasForceUpdate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDownloadLink()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, gameVersionCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(3, gameId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(4, getChannelGameIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBool(5, forceUpdate_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(6, getDownloadLinkBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, gameVersionCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, gameId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getChannelGameIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, forceUpdate_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getDownloadLinkBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        gameVersionCode_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        channelGameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        forceUpdate_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        downloadLink_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.gameVersionCode_ = gameVersionCode_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelGameId_ = channelGameId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.forceUpdate_ = forceUpdate_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.downloadLink_ = downloadLink_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.getDefaultInstance()) return this;
        if (other.hasGameVersionCode()) {
          setGameVersionCode(other.getGameVersionCode());
        }
        if (other.hasGameId()) {
          setGameId(other.getGameId());
        }
        if (other.hasChannelGameId()) {
          bitField0_ |= 0x00000004;
          channelGameId_ = other.channelGameId_;
          onChanged();
        }
        if (other.hasForceUpdate()) {
          setForceUpdate(other.getForceUpdate());
        }
        if (other.hasDownloadLink()) {
          bitField0_ |= 0x00000010;
          downloadLink_ = other.downloadLink_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGameVersionCode()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        if (!hasChannelGameId()) {
          
          return false;
        }
        if (!hasForceUpdate()) {
          
          return false;
        }
        if (!hasDownloadLink()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 gameVersionCode = 1;
      private long gameVersionCode_ ;
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       ** 游戏版本号，Builder号 
       * </pre>
       */
      public boolean hasGameVersionCode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       ** 游戏版本号，Builder号 
       * </pre>
       */
      public long getGameVersionCode() {
        return gameVersionCode_;
      }
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       ** 游戏版本号，Builder号 
       * </pre>
       */
      public Builder setGameVersionCode(long value) {
        bitField0_ |= 0x00000001;
        gameVersionCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 gameVersionCode = 1;</code>
       *
       * <pre>
       ** 游戏版本号，Builder号 
       * </pre>
       */
      public Builder clearGameVersionCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        gameVersionCode_ = 0L;
        onChanged();
        return this;
      }

      // required int64 gameId = 3;
      private long gameId_ ;
      /**
       * <code>required int64 gameId = 3;</code>
       *
       * <pre>
       ** 平台游戏ID 
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int64 gameId = 3;</code>
       *
       * <pre>
       ** 平台游戏ID 
       * </pre>
       */
      public long getGameId() {
        return gameId_;
      }
      /**
       * <code>required int64 gameId = 3;</code>
       *
       * <pre>
       ** 平台游戏ID 
       * </pre>
       */
      public Builder setGameId(long value) {
        bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 gameId = 3;</code>
       *
       * <pre>
       ** 平台游戏ID 
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = 0L;
        onChanged();
        return this;
      }

      // required string channelGameId = 4;
      private java.lang.Object channelGameId_ = "";
      /**
       * <code>required string channelGameId = 4;</code>
       *
       * <pre>
       ** 渠道游戏ID 
       * </pre>
       */
      public boolean hasChannelGameId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string channelGameId = 4;</code>
       *
       * <pre>
       ** 渠道游戏ID 
       * </pre>
       */
      public java.lang.String getChannelGameId() {
        java.lang.Object ref = channelGameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelGameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channelGameId = 4;</code>
       *
       * <pre>
       ** 渠道游戏ID 
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelGameIdBytes() {
        java.lang.Object ref = channelGameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelGameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channelGameId = 4;</code>
       *
       * <pre>
       ** 渠道游戏ID 
       * </pre>
       */
      public Builder setChannelGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelGameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channelGameId = 4;</code>
       *
       * <pre>
       ** 渠道游戏ID 
       * </pre>
       */
      public Builder clearChannelGameId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelGameId_ = getDefaultInstance().getChannelGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string channelGameId = 4;</code>
       *
       * <pre>
       ** 渠道游戏ID 
       * </pre>
       */
      public Builder setChannelGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelGameId_ = value;
        onChanged();
        return this;
      }

      // required bool forceUpdate = 5;
      private boolean forceUpdate_ ;
      /**
       * <code>required bool forceUpdate = 5;</code>
       *
       * <pre>
       ** 是否强制更新 
       * </pre>
       */
      public boolean hasForceUpdate() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required bool forceUpdate = 5;</code>
       *
       * <pre>
       ** 是否强制更新 
       * </pre>
       */
      public boolean getForceUpdate() {
        return forceUpdate_;
      }
      /**
       * <code>required bool forceUpdate = 5;</code>
       *
       * <pre>
       ** 是否强制更新 
       * </pre>
       */
      public Builder setForceUpdate(boolean value) {
        bitField0_ |= 0x00000008;
        forceUpdate_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool forceUpdate = 5;</code>
       *
       * <pre>
       ** 是否强制更新 
       * </pre>
       */
      public Builder clearForceUpdate() {
        bitField0_ = (bitField0_ & ~0x00000008);
        forceUpdate_ = false;
        onChanged();
        return this;
      }

      // required string downloadLink = 6;
      private java.lang.Object downloadLink_ = "";
      /**
       * <code>required string downloadLink = 6;</code>
       *
       * <pre>
       ** 下载链接 
       * </pre>
       */
      public boolean hasDownloadLink() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string downloadLink = 6;</code>
       *
       * <pre>
       ** 下载链接 
       * </pre>
       */
      public java.lang.String getDownloadLink() {
        java.lang.Object ref = downloadLink_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          downloadLink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string downloadLink = 6;</code>
       *
       * <pre>
       ** 下载链接 
       * </pre>
       */
      public com.google.protobuf.ByteString
          getDownloadLinkBytes() {
        java.lang.Object ref = downloadLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          downloadLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string downloadLink = 6;</code>
       *
       * <pre>
       ** 下载链接 
       * </pre>
       */
      public Builder setDownloadLink(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        downloadLink_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string downloadLink = 6;</code>
       *
       * <pre>
       ** 下载链接 
       * </pre>
       */
      public Builder clearDownloadLink() {
        bitField0_ = (bitField0_ & ~0x00000010);
        downloadLink_ = getDefaultInstance().getDownloadLink();
        onChanged();
        return this;
      }
      /**
       * <code>required string downloadLink = 6;</code>
       *
       * <pre>
       ** 下载链接 
       * </pre>
       */
      public Builder setDownloadLinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        downloadLink_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo)
    }

    static {
      defaultInstance = new GameVersionInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo)
  }

  public interface RequestGetGameVersionInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required int32 systemType = 2;
    /**
     * <code>required int32 systemType = 2;</code>
     *
     * <pre>
     * app 系统版本 0 未知 1 ios 2 android
     * </pre>
     */
    boolean hasSystemType();
    /**
     * <code>required int32 systemType = 2;</code>
     *
     * <pre>
     * app 系统版本 0 未知 1 ios 2 android
     * </pre>
     */
    int getSystemType();

    // required int64 sdkVersionCode = 3;
    /**
     * <code>required int64 sdkVersionCode = 3;</code>
     *
     * <pre>
     * SDK版本号，Builder号
     * </pre>
     */
    boolean hasSdkVersionCode();
    /**
     * <code>required int64 sdkVersionCode = 3;</code>
     *
     * <pre>
     * SDK版本号，Builder号
     * </pre>
     */
    long getSdkVersionCode();

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams> 
        getGameVersionParamsList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams getGameVersionParams(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    int getGameVersionParamsCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder> 
        getGameVersionParamsOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder getGameVersionParamsOrBuilder(
        int index);

    // optional int64 userId = 5;
    /**
     * <code>optional int64 userId = 5;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 5;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGameVersionInfo}
   *
   * <pre>
   * GameService.java
   * 获取游戏版本信息
   * domain = 4302, op = 63
   * </pre>
   */
  public static final class RequestGetGameVersionInfo extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetGameVersionInfoOrBuilder {
    // Use RequestGetGameVersionInfo.newBuilder() to construct.
    private RequestGetGameVersionInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetGameVersionInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetGameVersionInfo defaultInstance;
    public static RequestGetGameVersionInfo getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetGameVersionInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetGameVersionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              systemType_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              sdkVersionCode_ = input.readInt64();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                gameVersionParams_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams>();
                mutable_bitField0_ |= 0x00000008;
              }
              gameVersionParams_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.PARSER, extensionRegistry));
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              userId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          gameVersionParams_ = java.util.Collections.unmodifiableList(gameVersionParams_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetGameVersionInfo> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetGameVersionInfo>() {
      public RequestGetGameVersionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetGameVersionInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetGameVersionInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 systemType = 2;
    public static final int SYSTEMTYPE_FIELD_NUMBER = 2;
    private int systemType_;
    /**
     * <code>required int32 systemType = 2;</code>
     *
     * <pre>
     * app 系统版本 0 未知 1 ios 2 android
     * </pre>
     */
    public boolean hasSystemType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 systemType = 2;</code>
     *
     * <pre>
     * app 系统版本 0 未知 1 ios 2 android
     * </pre>
     */
    public int getSystemType() {
      return systemType_;
    }

    // required int64 sdkVersionCode = 3;
    public static final int SDKVERSIONCODE_FIELD_NUMBER = 3;
    private long sdkVersionCode_;
    /**
     * <code>required int64 sdkVersionCode = 3;</code>
     *
     * <pre>
     * SDK版本号，Builder号
     * </pre>
     */
    public boolean hasSdkVersionCode() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int64 sdkVersionCode = 3;</code>
     *
     * <pre>
     * SDK版本号，Builder号
     * </pre>
     */
    public long getSdkVersionCode() {
      return sdkVersionCode_;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;
    public static final int GAMEVERSIONPARAMS_FIELD_NUMBER = 4;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams> gameVersionParams_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams> getGameVersionParamsList() {
      return gameVersionParams_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder> 
        getGameVersionParamsOrBuilderList() {
      return gameVersionParams_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    public int getGameVersionParamsCount() {
      return gameVersionParams_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams getGameVersionParams(int index) {
      return gameVersionParams_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
     *
     * <pre>
     * 获取游戏版本参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder getGameVersionParamsOrBuilder(
        int index) {
      return gameVersionParams_.get(index);
    }

    // optional int64 userId = 5;
    public static final int USERID_FIELD_NUMBER = 5;
    private long userId_;
    /**
     * <code>optional int64 userId = 5;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int64 userId = 5;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    private void initFields() {
      appId_ = "";
      systemType_ = 0;
      sdkVersionCode_ = 0L;
      gameVersionParams_ = java.util.Collections.emptyList();
      userId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSystemType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSdkVersionCode()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getGameVersionParamsCount(); i++) {
        if (!getGameVersionParams(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, systemType_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, sdkVersionCode_);
      }
      for (int i = 0; i < gameVersionParams_.size(); i++) {
        output.writeMessage(4, gameVersionParams_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt64(5, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, systemType_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, sdkVersionCode_);
      }
      for (int i = 0; i < gameVersionParams_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, gameVersionParams_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetGameVersionInfo}
     *
     * <pre>
     * GameService.java
     * 获取游戏版本信息
     * domain = 4302, op = 63
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameVersionParamsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        systemType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        sdkVersionCode_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (gameVersionParamsBuilder_ == null) {
          gameVersionParams_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          gameVersionParamsBuilder_.clear();
        }
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.systemType_ = systemType_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.sdkVersionCode_ = sdkVersionCode_;
        if (gameVersionParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            gameVersionParams_ = java.util.Collections.unmodifiableList(gameVersionParams_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.gameVersionParams_ = gameVersionParams_;
        } else {
          result.gameVersionParams_ = gameVersionParamsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        result.userId_ = userId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasSystemType()) {
          setSystemType(other.getSystemType());
        }
        if (other.hasSdkVersionCode()) {
          setSdkVersionCode(other.getSdkVersionCode());
        }
        if (gameVersionParamsBuilder_ == null) {
          if (!other.gameVersionParams_.isEmpty()) {
            if (gameVersionParams_.isEmpty()) {
              gameVersionParams_ = other.gameVersionParams_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureGameVersionParamsIsMutable();
              gameVersionParams_.addAll(other.gameVersionParams_);
            }
            onChanged();
          }
        } else {
          if (!other.gameVersionParams_.isEmpty()) {
            if (gameVersionParamsBuilder_.isEmpty()) {
              gameVersionParamsBuilder_.dispose();
              gameVersionParamsBuilder_ = null;
              gameVersionParams_ = other.gameVersionParams_;
              bitField0_ = (bitField0_ & ~0x00000008);
              gameVersionParamsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGameVersionParamsFieldBuilder() : null;
            } else {
              gameVersionParamsBuilder_.addAllMessages(other.gameVersionParams_);
            }
          }
        }
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasSystemType()) {
          
          return false;
        }
        if (!hasSdkVersionCode()) {
          
          return false;
        }
        for (int i = 0; i < getGameVersionParamsCount(); i++) {
          if (!getGameVersionParams(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // required int32 systemType = 2;
      private int systemType_ ;
      /**
       * <code>required int32 systemType = 2;</code>
       *
       * <pre>
       * app 系统版本 0 未知 1 ios 2 android
       * </pre>
       */
      public boolean hasSystemType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 systemType = 2;</code>
       *
       * <pre>
       * app 系统版本 0 未知 1 ios 2 android
       * </pre>
       */
      public int getSystemType() {
        return systemType_;
      }
      /**
       * <code>required int32 systemType = 2;</code>
       *
       * <pre>
       * app 系统版本 0 未知 1 ios 2 android
       * </pre>
       */
      public Builder setSystemType(int value) {
        bitField0_ |= 0x00000002;
        systemType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 systemType = 2;</code>
       *
       * <pre>
       * app 系统版本 0 未知 1 ios 2 android
       * </pre>
       */
      public Builder clearSystemType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        systemType_ = 0;
        onChanged();
        return this;
      }

      // required int64 sdkVersionCode = 3;
      private long sdkVersionCode_ ;
      /**
       * <code>required int64 sdkVersionCode = 3;</code>
       *
       * <pre>
       * SDK版本号，Builder号
       * </pre>
       */
      public boolean hasSdkVersionCode() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int64 sdkVersionCode = 3;</code>
       *
       * <pre>
       * SDK版本号，Builder号
       * </pre>
       */
      public long getSdkVersionCode() {
        return sdkVersionCode_;
      }
      /**
       * <code>required int64 sdkVersionCode = 3;</code>
       *
       * <pre>
       * SDK版本号，Builder号
       * </pre>
       */
      public Builder setSdkVersionCode(long value) {
        bitField0_ |= 0x00000004;
        sdkVersionCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 sdkVersionCode = 3;</code>
       *
       * <pre>
       * SDK版本号，Builder号
       * </pre>
       */
      public Builder clearSdkVersionCode() {
        bitField0_ = (bitField0_ & ~0x00000004);
        sdkVersionCode_ = 0L;
        onChanged();
        return this;
      }

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams> gameVersionParams_ =
        java.util.Collections.emptyList();
      private void ensureGameVersionParamsIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          gameVersionParams_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams>(gameVersionParams_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder> gameVersionParamsBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams> getGameVersionParamsList() {
        if (gameVersionParamsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gameVersionParams_);
        } else {
          return gameVersionParamsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public int getGameVersionParamsCount() {
        if (gameVersionParamsBuilder_ == null) {
          return gameVersionParams_.size();
        } else {
          return gameVersionParamsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams getGameVersionParams(int index) {
        if (gameVersionParamsBuilder_ == null) {
          return gameVersionParams_.get(index);
        } else {
          return gameVersionParamsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder setGameVersionParams(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams value) {
        if (gameVersionParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.set(index, value);
          onChanged();
        } else {
          gameVersionParamsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder setGameVersionParams(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder builderForValue) {
        if (gameVersionParamsBuilder_ == null) {
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.set(index, builderForValue.build());
          onChanged();
        } else {
          gameVersionParamsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder addGameVersionParams(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams value) {
        if (gameVersionParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.add(value);
          onChanged();
        } else {
          gameVersionParamsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder addGameVersionParams(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams value) {
        if (gameVersionParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.add(index, value);
          onChanged();
        } else {
          gameVersionParamsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder addGameVersionParams(
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder builderForValue) {
        if (gameVersionParamsBuilder_ == null) {
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.add(builderForValue.build());
          onChanged();
        } else {
          gameVersionParamsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder addGameVersionParams(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder builderForValue) {
        if (gameVersionParamsBuilder_ == null) {
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.add(index, builderForValue.build());
          onChanged();
        } else {
          gameVersionParamsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder addAllGameVersionParams(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams> values) {
        if (gameVersionParamsBuilder_ == null) {
          ensureGameVersionParamsIsMutable();
          super.addAll(values, gameVersionParams_);
          onChanged();
        } else {
          gameVersionParamsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder clearGameVersionParams() {
        if (gameVersionParamsBuilder_ == null) {
          gameVersionParams_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          gameVersionParamsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public Builder removeGameVersionParams(int index) {
        if (gameVersionParamsBuilder_ == null) {
          ensureGameVersionParamsIsMutable();
          gameVersionParams_.remove(index);
          onChanged();
        } else {
          gameVersionParamsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder getGameVersionParamsBuilder(
          int index) {
        return getGameVersionParamsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder getGameVersionParamsOrBuilder(
          int index) {
        if (gameVersionParamsBuilder_ == null) {
          return gameVersionParams_.get(index);  } else {
          return gameVersionParamsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder> 
           getGameVersionParamsOrBuilderList() {
        if (gameVersionParamsBuilder_ != null) {
          return gameVersionParamsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gameVersionParams_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder addGameVersionParamsBuilder() {
        return getGameVersionParamsFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder addGameVersionParamsBuilder(
          int index) {
        return getGameVersionParamsFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionParams gameVersionParams = 4;</code>
       *
       * <pre>
       * 获取游戏版本参数
       * </pre>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder> 
           getGameVersionParamsBuilderList() {
        return getGameVersionParamsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder> 
          getGameVersionParamsFieldBuilder() {
        if (gameVersionParamsBuilder_ == null) {
          gameVersionParamsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParamsOrBuilder>(
                  gameVersionParams_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          gameVersionParams_ = null;
        }
        return gameVersionParamsBuilder_;
      }

      // optional int64 userId = 5;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 5;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int64 userId = 5;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 5;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000010;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 5;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGameVersionInfo)
    }

    static {
      defaultInstance = new RequestGetGameVersionInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetGameVersionInfo)
  }

  public interface ResponseGetGameVersionInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo> 
        getGameVersionInfosList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo getGameVersionInfos(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    int getGameVersionInfosCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder> 
        getGameVersionInfosOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder getGameVersionInfosOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameVersionInfo}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetGameVersionInfo extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetGameVersionInfoOrBuilder {
    // Use ResponseGetGameVersionInfo.newBuilder() to construct.
    private ResponseGetGameVersionInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetGameVersionInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetGameVersionInfo defaultInstance;
    public static ResponseGetGameVersionInfo getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetGameVersionInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetGameVersionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gameVersionInfos_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              gameVersionInfos_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gameVersionInfos_ = java.util.Collections.unmodifiableList(gameVersionInfos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetGameVersionInfo> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetGameVersionInfo>() {
      public ResponseGetGameVersionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetGameVersionInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetGameVersionInfo> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;
    public static final int GAMEVERSIONINFOS_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo> gameVersionInfos_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo> getGameVersionInfosList() {
      return gameVersionInfos_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder> 
        getGameVersionInfosOrBuilderList() {
      return gameVersionInfos_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    public int getGameVersionInfosCount() {
      return gameVersionInfos_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo getGameVersionInfos(int index) {
      return gameVersionInfos_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder getGameVersionInfosOrBuilder(
        int index) {
      return gameVersionInfos_.get(index);
    }

    private void initFields() {
      gameVersionInfos_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getGameVersionInfosCount(); i++) {
        if (!getGameVersionInfos(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gameVersionInfos_.size(); i++) {
        output.writeMessage(1, gameVersionInfos_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gameVersionInfos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameVersionInfos_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameVersionInfo}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.class, fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameVersionInfosFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameVersionInfosBuilder_ == null) {
          gameVersionInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gameVersionInfosBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo build() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo result = new fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo(this);
        int from_bitField0_ = bitField0_;
        if (gameVersionInfosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gameVersionInfos_ = java.util.Collections.unmodifiableList(gameVersionInfos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gameVersionInfos_ = gameVersionInfos_;
        } else {
          result.gameVersionInfos_ = gameVersionInfosBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo.getDefaultInstance()) return this;
        if (gameVersionInfosBuilder_ == null) {
          if (!other.gameVersionInfos_.isEmpty()) {
            if (gameVersionInfos_.isEmpty()) {
              gameVersionInfos_ = other.gameVersionInfos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGameVersionInfosIsMutable();
              gameVersionInfos_.addAll(other.gameVersionInfos_);
            }
            onChanged();
          }
        } else {
          if (!other.gameVersionInfos_.isEmpty()) {
            if (gameVersionInfosBuilder_.isEmpty()) {
              gameVersionInfosBuilder_.dispose();
              gameVersionInfosBuilder_ = null;
              gameVersionInfos_ = other.gameVersionInfos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gameVersionInfosBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGameVersionInfosFieldBuilder() : null;
            } else {
              gameVersionInfosBuilder_.addAllMessages(other.gameVersionInfos_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getGameVersionInfosCount(); i++) {
          if (!getGameVersionInfos(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo> gameVersionInfos_ =
        java.util.Collections.emptyList();
      private void ensureGameVersionInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gameVersionInfos_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo>(gameVersionInfos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder> gameVersionInfosBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo> getGameVersionInfosList() {
        if (gameVersionInfosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gameVersionInfos_);
        } else {
          return gameVersionInfosBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public int getGameVersionInfosCount() {
        if (gameVersionInfosBuilder_ == null) {
          return gameVersionInfos_.size();
        } else {
          return gameVersionInfosBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo getGameVersionInfos(int index) {
        if (gameVersionInfosBuilder_ == null) {
          return gameVersionInfos_.get(index);
        } else {
          return gameVersionInfosBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder setGameVersionInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo value) {
        if (gameVersionInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.set(index, value);
          onChanged();
        } else {
          gameVersionInfosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder setGameVersionInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder builderForValue) {
        if (gameVersionInfosBuilder_ == null) {
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.set(index, builderForValue.build());
          onChanged();
        } else {
          gameVersionInfosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder addGameVersionInfos(fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo value) {
        if (gameVersionInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.add(value);
          onChanged();
        } else {
          gameVersionInfosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder addGameVersionInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo value) {
        if (gameVersionInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.add(index, value);
          onChanged();
        } else {
          gameVersionInfosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder addGameVersionInfos(
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder builderForValue) {
        if (gameVersionInfosBuilder_ == null) {
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.add(builderForValue.build());
          onChanged();
        } else {
          gameVersionInfosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder addGameVersionInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder builderForValue) {
        if (gameVersionInfosBuilder_ == null) {
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.add(index, builderForValue.build());
          onChanged();
        } else {
          gameVersionInfosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder addAllGameVersionInfos(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo> values) {
        if (gameVersionInfosBuilder_ == null) {
          ensureGameVersionInfosIsMutable();
          super.addAll(values, gameVersionInfos_);
          onChanged();
        } else {
          gameVersionInfosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder clearGameVersionInfos() {
        if (gameVersionInfosBuilder_ == null) {
          gameVersionInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gameVersionInfosBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public Builder removeGameVersionInfos(int index) {
        if (gameVersionInfosBuilder_ == null) {
          ensureGameVersionInfosIsMutable();
          gameVersionInfos_.remove(index);
          onChanged();
        } else {
          gameVersionInfosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder getGameVersionInfosBuilder(
          int index) {
        return getGameVersionInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder getGameVersionInfosOrBuilder(
          int index) {
        if (gameVersionInfosBuilder_ == null) {
          return gameVersionInfos_.get(index);  } else {
          return gameVersionInfosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder> 
           getGameVersionInfosOrBuilderList() {
        if (gameVersionInfosBuilder_ != null) {
          return gameVersionInfosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gameVersionInfos_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder addGameVersionInfosBuilder() {
        return getGameVersionInfosFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder addGameVersionInfosBuilder(
          int index) {
        return getGameVersionInfosFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameVersionInfo gameVersionInfos = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder> 
           getGameVersionInfosBuilderList() {
        return getGameVersionInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder> 
          getGameVersionInfosFieldBuilder() {
        if (gameVersionInfosBuilder_ == null) {
          gameVersionInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo.Builder, fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfoOrBuilder>(
                  gameVersionInfos_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gameVersionInfos_ = null;
        }
        return gameVersionInfosBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameVersionInfo)
    }

    static {
      defaultInstance = new ResponseGetGameVersionInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetGameVersionInfo)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023protocol_game.proto\022-fm.lizhi.commons." +
      "template.datacenter.protocol\"\213\001\n\nGameDet" +
      "ail\022\016\n\006gameId\030\001 \002(\003\022\022\n\nrenderType\030\002 \002(\t\022" +
      "\025\n\rchannelGameId\030\003 \002(\t\022\017\n\007channel\030\004 \002(\t\022" +
      "\016\n\006config\030\005 \002(\t\022\020\n\010gameName\030\006 \002(\t\022\017\n\007cap" +
      "tain\030\007 \002(\005\"3\n\022GetGameDetailParam\022\r\n\005appI" +
      "d\030\001 \002(\t\022\016\n\006gameId\030\002 \002(\t\"h\n\024RequestGetGam" +
      "eDetail\022P\n\005param\030\001 \001(\0132A.fm.lizhi.common" +
      "s.template.datacenter.protocol.GetGameDe" +
      "tailParam\"f\n\025ResponseGetGameDetail\022M\n\nga",
      "meDetail\030\001 \001(\01329.fm.lizhi.commons.templa" +
      "te.datacenter.protocol.GameDetail\"F\n\025Upd" +
      "ateGameConfigParam\022\r\n\005appId\030\001 \002(\t\022\016\n\006gam" +
      "eId\030\002 \002(\t\022\016\n\006config\030\003 \002(\t\"n\n\027RequestUpda" +
      "teGameConfig\022S\n\005param\030\001 \001(\0132D.fm.lizhi.c" +
      "ommons.template.datacenter.protocol.Upda" +
      "teGameConfigParam\"\032\n\030ResponseUpdateGameC" +
      "onfig\"<\n\021GameVersionParams\022\027\n\017gameVersio" +
      "nCode\030\001 \002(\003\022\016\n\006gameId\030\002 \002(\t\"|\n\017GameVersi" +
      "onInfo\022\027\n\017gameVersionCode\030\001 \002(\003\022\016\n\006gameI",
      "d\030\003 \002(\003\022\025\n\rchannelGameId\030\004 \002(\t\022\023\n\013forceU" +
      "pdate\030\005 \002(\010\022\024\n\014downloadLink\030\006 \002(\t\"\303\001\n\031Re" +
      "questGetGameVersionInfo\022\r\n\005appId\030\001 \002(\t\022\022" +
      "\n\nsystemType\030\002 \002(\005\022\026\n\016sdkVersionCode\030\003 \002" +
      "(\003\022[\n\021gameVersionParams\030\004 \003(\<EMAIL>" +
      ".commons.template.datacenter.protocol.Ga" +
      "meVersionParams\022\016\n\006userId\030\005 \001(\003\"v\n\032Respo" +
      "nseGetGameVersionInfo\022X\n\020gameVersionInfo" +
      "s\030\001 \003(\0132>.fm.lizhi.commons.template.data" +
      "center.protocol.GameVersionInfoB2\n\034fm.li",
      "zhi.ocean.seal.protocolB\020GameServiceProt" +
      "oH\001"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameDetail_descriptor,
              new java.lang.String[] { "GameId", "RenderType", "ChannelGameId", "Channel", "Config", "GameName", "Captain", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GetGameDetailParam_descriptor,
              new java.lang.String[] { "AppId", "GameId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameDetail_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameDetail_descriptor,
              new java.lang.String[] { "GameDetail", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_UpdateGameConfigParam_descriptor,
              new java.lang.String[] { "AppId", "GameId", "Config", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateGameConfig_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateGameConfig_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionParams_descriptor,
              new java.lang.String[] { "GameVersionCode", "GameId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameVersionInfo_descriptor,
              new java.lang.String[] { "GameVersionCode", "GameId", "ChannelGameId", "ForceUpdate", "DownloadLink", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetGameVersionInfo_descriptor,
              new java.lang.String[] { "AppId", "SystemType", "SdkVersionCode", "GameVersionParams", "UserId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_descriptor =
            getDescriptor().getMessageTypes().get(10);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetGameVersionInfo_descriptor,
              new java.lang.String[] { "GameVersionInfos", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
