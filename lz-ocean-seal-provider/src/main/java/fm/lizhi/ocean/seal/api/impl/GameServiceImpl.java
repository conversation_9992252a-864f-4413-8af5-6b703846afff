package fm.lizhi.ocean.seal.api.impl;

import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.adapter.GameVersionAdapter;
import fm.lizhi.ocean.seal.api.GameService;
import fm.lizhi.ocean.seal.constant.RenderType;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GameVersionBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameInfoManager;
import fm.lizhi.ocean.seal.manager.GameVersionManager;
import fm.lizhi.ocean.seal.pojo.bo.GameLastVersionParam;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * Created in 2022-04-27 17:44.
 *
 * <AUTHOR>
 */
@ServiceProvider
public class GameServiceImpl implements GameService {
    private static final Logger logger = LoggerFactory.getLogger(GameServiceImpl.class);
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private GameVersionManager gameVersionManager;
    @Inject
    private GameVersionAdapter gameVersionAdapter;

    /**
     * 获取游戏详情
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 游戏ID不存在<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameServiceProto.ResponseGetGameDetail> getGameDetail(GameServiceProto.GetGameDetailParam param) {
        GameServiceProto.ResponseGetGameDetail.Builder builder = GameServiceProto.ResponseGetGameDetail.newBuilder();
        String appId = param.getAppId();
        String gameId = param.getGameId();
        logger.info("Get game detail, appId:{}, gameId:{}", appId, gameId);
        try {
            // 查询游戏信息
            GameBizGameBean bizGameBean = this.bizGameManager.getGame(appId, gameId);
            if (bizGameBean == null) {
                logger.error("Failed to load game, game not exists, gameId:{}, appId:{}", gameId, appId);
                return new Result<>(GET_GAME_DETAIL_NOT_EXISTS, builder.build());
            }

            // 合并配置
            String config = this.bizGameManager.mergeConfig(bizGameBean.getId(), bizGameBean.getConfig());

            // 渠道游戏信息
            GameInfoBean gameInfoBean = this.gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
            if (gameInfoBean == null) {
                logger.error("Failed to get game detail, game info not exists, appId:{}, gameId:{}", appId, gameId);
                return new Result<>(GET_GAME_DETAIL_NOT_EXISTS, builder.build());
            }

            GameServiceProto.GameDetail.Builder detailBuilder = GameServiceProto.GameDetail.newBuilder();
            detailBuilder.setGameId(bizGameBean.getId())
                    .setRenderType(RenderType.from(gameInfoBean.getRenderType()).getType())
                    .setChannelGameId(gameInfoBean.getChannelGameIdStr())
                    .setChannel(gameInfoBean.getChannel())
                    .setConfig(Optional.ofNullable(config).orElse(StringUtils.EMPTY))
                    .setGameName(gameInfoBean.getName())
                    .setCaptain(gameInfoBean.getCaptain());
            builder.setGameDetail(detailBuilder.build());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            logger.error("Exception to get game detail, appId:{}, gameId:{}, msg:{}", appId, gameId, e.getMessage(), e);
        }
        return new Result<>(GET_GAME_DETAIL_ERROR, builder.build());
    }

    /**
     * 更新游戏配置信息
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 游戏ID不存在<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameServiceProto.ResponseUpdateGameConfig> updateGameConfig(GameServiceProto.UpdateGameConfigParam param) {
        GameServiceProto.ResponseUpdateGameConfig.Builder builder = GameServiceProto.ResponseUpdateGameConfig.newBuilder();
        String gameId = param.getGameId();
        String appId = param.getAppId();
        String config = param.getConfig();
        try {
            logger.info("Start the game round, gameId:{}, appId:{}, config:{}", gameId, appId, config);
            if (StringUtils.isEmpty(gameId) || StringUtils.isEmpty(appId)) {
                logger.warn("Failed to start game round, param error, gameId:{}, appId:{}, config:{}", gameId, appId, config);
                return new Result<>(UPDATE_GAME_CONFIG_ILLEGAL_PARAMS, builder.build());
            }

            // 查询游戏信息
            GameBizGameBean bizGameBean = this.bizGameManager.getGame(appId, gameId);
            if (bizGameBean == null) {
                logger.error("Failed to start game round, game not exists, gameId:{}, appId:{}, config:{}", gameId, appId, config);
                return new Result<>(UPDATE_GAME_CONFIG_NOT_EXISTS, builder.build());
            }

            // 更新游戏配置信息
            this.bizGameManager.updateGameConfig(bizGameBean.getId(), config);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            logger.error("Exception to start game round, gameId:{}, appId:{}, config:{}, msg:{}", gameId, appId, config, e.getMessage(), e);
        }
        return new Result<>(UPDATE_GAME_CONFIG_ERROR, builder.build());
    }

    /**
     * 查询 某个业务 关联的游戏 支持的seal sdk最低版本的最大游戏版本号。
     * 例如： funbox 关联 ludo游戏  嵌入的 sealSdk 版本为 1000 游戏版本为100
     * 现在存在游戏版本记录： funbox ludo游戏 sealSdk版本最低为999 另外游戏版本已经更新到102
     * 那么根据对比判断。将会查询到 funbox ludo minsealsdk 999 gameversion 102 的记录
     * 这里为什么需要引入appId。理论上是不需要考虑业务的，只需要考虑游戏。但是目前存在的情况是。不同业务定制了不同的seal sdk。而这里的版本又存在重叠。所以需要增加appId
     *
     * 8月24号，增加A/B测试，白名单功能
     * 命中白名单：ext_config TO JSON ，获取whiteList属性，形成List，判断userId是否存在List中
     * 命中A/B流量：
     * 配置命中A，需要下发新版游戏包的流量概率ratio为：10%
     * Boolean hitFlow（userId, ratio）{
     *   - if(ratio == 0) return false;
     *   - else if (ratio == 100) return true;
     *   - hashId = hash(userId) ， 保证userId符合均匀分布;
     *   - 计算出命中比率的因子ratioFactor = 1/ratio
     *   - 判断是否命中 return hashId % ratioFactor == 0;
     * }
     *
     *
     * @param appId             业务方appId
     * @param systemType        app 系统版本 0 未知 1 ios 2 android
     * @param sdkVersionCode    SDK版本号，Builder号
     * @param gameVersionParams 获取游戏版本参数
     * @param userId 用户ID
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameServiceProto.ResponseGetGameVersionInfo> getGameVersionInfo(String appId, int systemType, long sdkVersionCode, List<GameServiceProto.GameVersionParams> gameVersionParams, long userId) {
        LogContext.addReqLog("appId={}`systemType={}`sdkVersionCode={}`userId={}", appId, systemType, sdkVersionCode, userId);
        LogContext.addResLog("appId={}`systemType={}`sdkVersionCode={}`userId={}", appId, systemType, sdkVersionCode, userId);

        if (StringUtils.isBlank(appId)) {
            logger.warn("Get game version information parameter verification failed, appId:{}, systemType:{}, sdkVersion:{}"
                    , appId, systemType, sdkVersionCode);
            return new Result<>(GET_GAME_VERSION_INFO_ILLEGAL_PARAMS, null);
        }
        try {
            List<GameVersionBean> beans = this.gameVersionManager.getGameLatestVersion(
                    GameLastVersionParam.builder().appId(appId).params(gameVersionParams)
                            .sdkVersionCode(sdkVersionCode).systemType(systemType).userId(userId).build());

            GameServiceProto.ResponseGetGameVersionInfo.Builder builder = GameServiceProto.ResponseGetGameVersionInfo.newBuilder();
            builder.addAllGameVersionInfos(this.gameVersionAdapter.convert(beans));

            LogContext.addResLog("size={}", beans.size());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            logger.error("Get game information error, appId:{}, systemType:{}, sdkVersion:{}", appId, systemType, sdkVersionCode, e);
            return new Result<>(GET_GAME_VERSION_INFO_ERROR, null);
        }
    }
}