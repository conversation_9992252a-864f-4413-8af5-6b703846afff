package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameDetail;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameDetail;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestUpdateGameConfig;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseUpdateGameConfig;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.RequestGetGameVersionInfo;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.ResponseGetGameVersionInfo;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.GameDetail;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.GetGameDetailParam;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.UpdateGameConfigParam;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionParams;
import fm.lizhi.ocean.seal.protocol.GameServiceProto.GameVersionInfo;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameService {
	
	
	/**
	 *  获取游戏详情
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 游戏ID不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 61, request = RequestGetGameDetail.class, response = ResponseGetGameDetail.class)
	@Return(resultType = ResponseGetGameDetail.class)
	Result<ResponseGetGameDetail> getGameDetail(@Attribute(name = "param") GetGameDetailParam param);
	
	
	/**
	 *  更新游戏配置信息
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 游戏ID不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 62, request = RequestUpdateGameConfig.class, response = ResponseUpdateGameConfig.class)
	@Return(resultType = ResponseUpdateGameConfig.class)
	Result<ResponseUpdateGameConfig> updateGameConfig(@Attribute(name = "param") UpdateGameConfigParam param);
	
	
	/**
	 *  获取游戏版本信息
	 *
	 * @param appId
	 *            业务方appId
	 * @param systemType
	 *            app 系统版本 0 未知 1 ios 2 android
	 * @param sdkVersionCode
	 *            SDK版本号，Builder号
	 * @param gameVersionParams
	 *            获取游戏版本参数
	 * @param userId
	 *            用户ID
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 63, request = RequestGetGameVersionInfo.class, response = ResponseGetGameVersionInfo.class)
	@Return(resultType = ResponseGetGameVersionInfo.class)
	Result<ResponseGetGameVersionInfo> getGameVersionInfo(@Attribute(name = "appId") String appId, @Attribute(name = "systemType") int systemType, @Attribute(name = "sdkVersionCode") long sdkVersionCode, @Attribute(name = "gameVersionParams", genericType = GameVersionParams.class) List<GameVersionParams> gameVersionParams, @Attribute(name = "userId") long userId);
	
	
	public static final int GET_GAME_DETAIL_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_DETAIL_NOT_EXISTS = 2; // 游戏ID不存在
	public static final int GET_GAME_DETAIL_ERROR = 3; // 内部错误

	public static final int UPDATE_GAME_CONFIG_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int UPDATE_GAME_CONFIG_NOT_EXISTS = 2; // 游戏ID不存在
	public static final int UPDATE_GAME_CONFIG_ERROR = 3; // 内部错误

	public static final int GET_GAME_VERSION_INFO_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_VERSION_INFO_ERROR = 3; // 内部错误


}