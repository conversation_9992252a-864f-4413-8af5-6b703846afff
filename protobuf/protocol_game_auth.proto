package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameAuthServiceProto";
option optimize_for = SPEED;

message LoginToken{
    optional string token = 1; //根据UID生成的短期令牌
    optional int64 expireDate = 2; //短期令牌过期时间戳（毫秒）
}

message ServerToken{
    optional string token = 1; //根据UID生成的长期令牌
    optional int64 expireDate = 2; //长期令牌过期时间戳（毫秒）
    optional int32 errorCode = 3; //服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
    optional string uid = 4; //根据code或者token解析出来的uid
}

message GameUser{
    optional int64 userId = 1; //业务用户ID
    optional string gameUserId = 2; //游戏用户ID
    optional string appId = 3; //所属appId
    optional int32 errorCode = 4; //服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
}

// GameAuthService.java
// 获取短期令牌，默认时长2小时
// domain = 4302, op = 1
message RequestGetLoginToken {
    optional int64 userId = 1; //用户ID
    optional string appId = 2; //appId
    optional string channel = 3; //游戏渠道
}

message ResponseGetLoginToken {
    optional LoginToken token = 1;
}

// GameAuthService.java
// 获取长期令牌SSToken，SSToken为小游戏服务端与业务服务端数据交换的令牌
// domain = 4302, op = 2
message RequestGetServerToken {
    optional string loginToken = 1; //短期令牌
    optional string channel = 2; //游戏渠道
    optional string appId = 3; //appId
}

message ResponseGetServerToken {
    optional ServerToken token = 1;
}

// GameAuthService.java
// 更新并返回长期令牌
// domain = 4302, op = 3
message RequestUpdateServerToken {
    optional string token = 1; //长期令牌
    optional string channel = 2; //游戏渠道
    optional string appId = 3; //appId
}

message ResponseUpdateServerToken {
    optional ServerToken token = 1;
}

// GameAuthService.java
// 通过长期令牌获得用户
// domain = 4302, op = 4
message RequestGetUserByServerToken {
    optional string token = 1; //长期令牌
    optional string channel = 2; //游戏渠道
    optional string appId = 3; //appId
}

message ResponseGetUserByServerToken {
    optional GameUser gameUser = 1;
}

// GameAuthService.java
// 校验短期令牌有效性
// domain = 4302, op = 5
message RequestVerifyLoginToken {
    optional string token = 1; //短期令牌
    optional string channel = 2; //游戏渠道
    optional string appId = 3; //appId
}

message ResponseVerifyLoginToken {
    optional int32 errorCode = 1;
}

// GameAuthService.java
// 校验长期令牌有效性
// domain = 4302, op = 6
message RequestVerifyServerToken {
    optional string token = 1; //长期令牌
    optional string channel = 2; //游戏渠道
    optional string appId = 3; //appId
}

message ResponseVerifyServerToken {
    optional int32 errorCode = 1;
}