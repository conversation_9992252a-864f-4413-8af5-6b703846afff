package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameAppServiceProto";
option optimize_for = SPEED;

/*获取回调配置信息参数*/
message GetAppCallbackParam {
  required string appId = 1; // appId
  optional int32 type = 2; // 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
}

/*回调配置信息*/
message AppCallback {
  required string appId = 1; // appId
  required int32 type = 2; // 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
  required string callbackKey = 3; // 接口回调Key
  required string callbackGetUserUrl = 4; // 获取用户信息地址
}

message ChannelInfo{
    optional int64 id = 1;
    optional string channel = 2;
    optional string channelAppId = 3;
    optional string channelAppKey = 4;
    optional string channelAppSecret = 5;
}

/*平台颁发给业务的信息表*/
message GameAppInfo{
    optional int64 id = 1;
    optional string appId = 2;
    optional string appSecret = 3;
    optional string appName = 4;
    optional string appAlias = 5;
    optional string appTopic = 6;
    repeated ChannelInfo channelInfos = 7;
}

// GameAppService.java
// 获取App回调配置信息
// domain = 4302, op = 30
message RequestGetAppCallback {
  optional GetAppCallbackParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = app不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetAppCallback {
  optional AppCallback appConfig = 1;
}

// GameAppService.java
// 根据AppId获取游戏信息
// domain = 4302, op = 31
message RequestGetAppInfoByAppId {
  optional string appId = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = app不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetAppInfoByAppId {
  optional GameAppInfo gameAppInfo = 1;
}