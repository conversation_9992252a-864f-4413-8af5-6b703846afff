package fm.lizhi.ocean.seal.xxl;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.common.kafka.common.util.CollectionUtils;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.mapper.GameCallbackMapper;
import fm.lizhi.ocean.seal.http.HttpClient;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameProxyManager;
import fm.lizhi.ocean.seal.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化业务回调配置表
 *
 * <AUTHOR>
 * 2025年4月10日 16:15:39
 */
@Slf4j
@JobHandler("initGameCallbackJob")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class InitGameCallbackJob extends IJobHandler {
    @Inject
    private GuidGenerator guidGenerator;

    @Inject
    private HttpClient httpClient;
    @Inject
    private GameCallbackMapper gameCallbackMapper;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private GameProxyManager gameProxyManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private BizGameManager bizGameManager;

    private static final String SUD_API_SETTLE_INFO = "https://asc.sudden.ltd/{sign}";
    private static final String SUD_CHANNEL = "sud";


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        return doExecute(StringUtils.isEmpty(s) ? "" : s.trim());
    }

    private ReturnT<String> doExecute(String param) {
        log.info("InitGameCallbackJob start time:{},param:{}", TimeUtils.formatDateTime(new Date(), "yyyy-MM-dd HH:mm:ss"), param);

        // 查询sud
        try {
            // 查询channel
            List<GameChannelBean> gameChannels = gameChannelManager.getGameChannelByChannel(SUD_CHANNEL);
            if (CollectionUtils.isNullOrEmpty(gameChannels)) {
                log.warn("InitGameCallbackJob getAllGameChannels is null");
                return ReturnT.SUCCESS;
            }
            for (GameChannelBean gameChannelBean : gameChannels) {
                // 渠道appId
                String channelAppId = gameChannelBean.getAppId();

                // app服务签名
                byte[] key = gameChannelBean.getAppSecret().getBytes();
                HMac mac = new HMac(HmacAlgorithm.HmacMD5, key);
                String appServerSign = mac.digestHex(channelAppId);

                // 查询sud服务接口API配置
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                String url = SUD_API_SETTLE_INFO.replace("{sign}", appServerSign);
                JSONObject result = this.httpClient.getJsonObject(url, null, headers);
                JSONObject api = result.getJSONObject("api");
                String pushEvent = api.getString("push_event");

                // 根据渠道查询业务游戏表
                List<GameBizGameBean> games = bizGameManager.getGames(gameChannelBean.getId());
                if (CollectionUtils.isNullOrEmpty(games)) {
                    log.warn("InitGameCallbackJob getGames is null gameChannelBean={}`", gameChannelBean);
                    continue;
                }

                for (GameBizGameBean gameBean : games) {
                    // 查询业务appId
                    GameAppBean gameApp = gameAppManager.getGameAppBeanById(gameBean.getGameAppId());
                    // 查询是否已存在推送事件配置
                    GameCallback beanTemp = new GameCallback();
                    beanTemp.setAppId(gameApp.getAppId());
                    beanTemp.setCallbackKey(String.valueOf(gameBean.getId()));
                    beanTemp.setEventName("push_event");
                    GameCallback gameCallback = gameCallbackMapper.selectOne(beanTemp);
                    Date date = new Date();
                    if (gameCallback == null) {
                        // 写入业务回调配置
                        GameCallback bean = new GameCallback();
                        bean.setId(guidGenerator.genId());
                        bean.setCreateTime(date);
                        bean.setAppId(gameApp.getAppId());
                        bean.setCallbackKey(String.valueOf(gameBean.getId()));
                        bean.setType(gameProxyManager.callBackType());
                        bean.setUrl(pushEvent);
                        bean.setModifyTime(date);
                        bean.setEventName("push_event");
                        gameCallbackMapper.insert(bean);
                    } else {
                        gameCallback.setUrl(pushEvent);
                        gameCallback.setModifyTime(date);
                        // 更新业务回调配置表
                        gameCallbackMapper.updateByPrimaryKey(gameCallback);
                    }
                }
            }
            log.info("InitGameCallbackJob end time:{},param:{}", TimeUtils.formatDateTime(new Date(), "yyyy-MM-dd HH:mm:ss"), param);
        } catch (Exception e) {
            log.error("InitGameCallbackJob Call invoke target interface error, exception", e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }
}
