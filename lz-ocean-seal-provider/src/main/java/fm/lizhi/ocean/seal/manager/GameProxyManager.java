package fm.lizhi.ocean.seal.manager;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameCallbackType;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.http.HttpClient;
import fm.lizhi.ocean.seal.pojo.bo.sud.SudPushEvent;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import fm.lizhi.ocean.seal.util.EnvUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Created in 2022-06-23 11:16.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameProxyManager {
    private static final Logger logger = LoggerFactory.getLogger(GameProxyManager.class);
    @Inject
    private HttpClient httpClient;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private GameCallbackManager gameCallbackManager;

    /**
     * 调用目标方法
     *
     * @param param 参数
     * @return
     */
    public GameProxyServiceProto.ResponseInvokeTarget invokeTarget(GameProxyServiceProto.InvokeTargetParams param) {
        String jsonParams = JsonFormat.printToString(param);
        logger.info("Call invoke target,params:{}", jsonParams);

        String url = null;

        try {
            //查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
            GameBizGameBean bizGameBean = bizGameManager.getGame(param.getAppId(), param.getGameId());
            if (bizGameBean == null) {
                throw new IllegalArgumentException("set header sign that gameId not exist.`appId={" + param.getAppId() + "}`gameId={" + param.getGameId() + "}");
            }
            // 查询渠道接口url
            url = this.getTargetInterfaceUrl(param, bizGameBean);
            if (url == null) {
                logger.error("Call invoke target interface error, url is null, params:{}", jsonParams);
                throw new RuntimeException("url is null");
            }

            // 设置请求头信息
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset=utf-8");

            String body = setHeaderSign(headers, param, bizGameBean);
            // 发送请求
            JSONObject result = this.httpClient.postJsonObject(url, null, headers, body);
            logger.info("Call invoke target, url:{}, params:{}, result:{}, body:{}", url, jsonParams, result, body);
            GameProxyServiceProto.ResponseInvokeTarget.Builder builder = GameProxyServiceProto.ResponseInvokeTarget.newBuilder();
            if (result.containsKey("ret_code") && result.containsKey("ret_msg")) {
                builder.setBizCode(result.getIntValue("ret_code"));
                builder.setMsg(Optional.ofNullable(result.getString("ret_msg")).orElse(StringUtils.EMPTY));
                builder.setData(Optional.ofNullable(result.getString("data")).orElse(StringUtils.EMPTY));
                return builder.build();
            } else {
                logger.warn("Call invoke target interface error, result illegal, params:{}, result:{}", jsonParams, result);
                throw new RuntimeException("result illegal, " + result);
            }
        } catch (Exception e) {
            logger.error("Call invoke target interface error, exception, url:{}, params:{}", url, jsonParams, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取目标接口的URL地址
     * 游戏调用业务, type = 1，需要传入该游戏调用哪个业务。 所以appId 为 业务的appId.  gameId 为 渠道的游戏ID
     * <p>
     * 业务调用游戏 type = 2, 需要传入调用的
     *
     * @param param 参数
     * @return
     */
    private String getTargetInterfaceUrl(GameProxyServiceProto.InvokeTargetParams param, GameBizGameBean bizGameBean) {
        String url = null;
        GameCallback gameCallback = gameCallbackManager.getGameCallback(param.getAppId(), String.valueOf(bizGameBean.getId()), callBackType(), "push_event");
        if (gameCallback == null || StringUtils.isBlank(gameCallback.getUrl())) {
            return null;
        }

        if (StringUtils.isNotBlank(gameCallback.getUrl())) {
            url = gameCallback.getUrl();
        }
        return url;
    }

    public int callBackType() {
        boolean pro = EnvUtils.isOnline();
        return (pro ? GameCallbackType.PRO_GAME_PROXY_INTERFACE.getValue() : GameCallbackType.GAME_PROXY_INTERFACE.getValue());
    }

    private boolean isCallBiz(int type) {
        return NumberUtils.INTEGER_ONE.intValue() == type;
    }

    /**
     * 设置签名
     * 通过判断 是业务调用游戏，还是游戏调用业务。
     * 如果是业务调用游戏，appId 为业务ID, gameId为给到业务的seal 的 游戏ID(game_biz_game ID). 这里如果调用游戏验签。首先需要把gameId改为渠道ID
     * 如果是游戏调用业务，appId 为业务ID, gameId为渠道游戏的ID.调用业务时，要转为seal 的 游戏ID(game_biz_game ID)
     * <p>
     * 但是需要注意，这里存在兼容的情况。如果是以前的旧版。业务自己嵌入渠道游戏的ID.这时，返回与生成签名都为 渠道gameId
     * 怎么区分是渠道gameId 还是 seal 里面分配的gameId，这里需要通过game_biz_game表进行判断。如果ID存在game_biz_game，即为seal 平台ID.
     * 否则 查询 game_biz_game_relation 兼容表的channel_game_id字段。存在，这个ID即为渠道ID
     *
     * @param headers
     * @param param
     * @param param
     */
    private String setHeaderSign(Map<String, String> headers, GameProxyServiceProto.InvokeTargetParams param, GameBizGameBean bizGameBean) {

        // 游戏调用业务 参数gameId为游戏渠道gameId.但是因为调用业务，所以需要转化为seal平台游戏ID(game_biz_game ID)
        // 另外传入业务的key 6518293  secret 6518293
        // 否则，业务调用游戏服务 参数gameId 为seal平台游戏ID(game_biz_game ID)，需要转化为游戏渠道gameId. 游戏服务key 30129761 secret 30129761
        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
        if (null == gameInfoBean) {
            throw new IllegalArgumentException("set header sign that gameInfo not exist.`appId={" + param.getAppId() + "}`gameId={" + param.getGameId() + "}");
        }
        String gameId = gameInfoBean.getChannelGameIdStr();

        // 请求时间戳（发送请求的时间戳）
        String timestamp = System.currentTimeMillis() + "";

        Map<String, Object> entry = new HashMap<>();
        entry.put("event", param.getEventName());
        entry.put("mg_id", gameId);
        entry.put("timestamp", timestamp);
        Map dataMap = JSONObject.parseObject(param.getDataJson(), Map.class);
        entry.put("data", dataMap);

        // 业务调用游戏服务 游戏服务key 30129761 secret 30129761
        GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
        if (null == gameChannelBean) {
            throw new IllegalArgumentException("set header sign that channel not exist.`appId={" + param.getAppId() + "}`gameId={" + param.getGameId() + "}");
        }

        // 随机字符串 (自定义随机字符串)
        String nonce = UUID.randomUUID().toString();
        // 请求body
        String body = JsonUtil.dumps(entry);

        // 签名串
        String signContent = String.format("%s\n%s\n%s\n%s\n", gameChannelBean.getAppId(), timestamp, nonce, body);
        // 签名值
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, gameChannelBean.getAppSecret().getBytes());
        String signature = hMac.digestHex(signContent);
        headers.put("Authorization", "Sud-Auth app_id=\""+gameChannelBean.getAppId()+"\",nonce=\""+nonce+"\",timestamp=\""+timestamp+"\",signature=\""+signature+"\"");
        return body;
    }
}