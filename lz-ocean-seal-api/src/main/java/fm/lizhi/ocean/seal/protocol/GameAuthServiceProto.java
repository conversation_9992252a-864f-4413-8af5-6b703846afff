// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_auth.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameAuthServiceProto {
  private GameAuthServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface LoginTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的短期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的短期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的短期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional int64 expireDate = 2;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *短期令牌过期时间戳（毫秒）
     * </pre>
     */
    boolean hasExpireDate();
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *短期令牌过期时间戳（毫秒）
     * </pre>
     */
    long getExpireDate();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.LoginToken}
   */
  public static final class LoginToken extends
      com.google.protobuf.GeneratedMessage
      implements LoginTokenOrBuilder {
    // Use LoginToken.newBuilder() to construct.
    private LoginToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private LoginToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final LoginToken defaultInstance;
    public static LoginToken getDefaultInstance() {
      return defaultInstance;
    }

    public LoginToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private LoginToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireDate_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder.class);
    }

    public static com.google.protobuf.Parser<LoginToken> PARSER =
        new com.google.protobuf.AbstractParser<LoginToken>() {
      public LoginToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LoginToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<LoginToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的短期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的短期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的短期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 expireDate = 2;
    public static final int EXPIREDATE_FIELD_NUMBER = 2;
    private long expireDate_;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *短期令牌过期时间戳（毫秒）
     * </pre>
     */
    public boolean hasExpireDate() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *短期令牌过期时间戳（毫秒）
     * </pre>
     */
    public long getExpireDate() {
      return expireDate_;
    }

    private void initFields() {
      token_ = "";
      expireDate_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, expireDate_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireDate_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.LoginToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        expireDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.expireDate_ = expireDate_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasExpireDate()) {
          setExpireDate(other.getExpireDate());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的短期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的短期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的短期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的短期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的短期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的短期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional int64 expireDate = 2;
      private long expireDate_ ;
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *短期令牌过期时间戳（毫秒）
       * </pre>
       */
      public boolean hasExpireDate() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *短期令牌过期时间戳（毫秒）
       * </pre>
       */
      public long getExpireDate() {
        return expireDate_;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *短期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder setExpireDate(long value) {
        bitField0_ |= 0x00000002;
        expireDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *短期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder clearExpireDate() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireDate_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.LoginToken)
    }

    static {
      defaultInstance = new LoginToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.LoginToken)
  }

  public interface ServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的长期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的长期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的长期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional int64 expireDate = 2;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    boolean hasExpireDate();
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    long getExpireDate();

    // optional int32 errorCode = 3;
    /**
     * <code>optional int32 errorCode = 3;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 3;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    int getErrorCode();

    // optional string uid = 4;
    /**
     * <code>optional string uid = 4;</code>
     *
     * <pre>
     *根据code或者token解析出来的uid
     * </pre>
     */
    boolean hasUid();
    /**
     * <code>optional string uid = 4;</code>
     *
     * <pre>
     *根据code或者token解析出来的uid
     * </pre>
     */
    java.lang.String getUid();
    /**
     * <code>optional string uid = 4;</code>
     *
     * <pre>
     *根据code或者token解析出来的uid
     * </pre>
     */
    com.google.protobuf.ByteString
        getUidBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ServerToken}
   */
  public static final class ServerToken extends
      com.google.protobuf.GeneratedMessage
      implements ServerTokenOrBuilder {
    // Use ServerToken.newBuilder() to construct.
    private ServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ServerToken defaultInstance;
    public static ServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public ServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireDate_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              errorCode_ = input.readInt32();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              uid_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ServerToken> PARSER =
        new com.google.protobuf.AbstractParser<ServerToken>() {
      public ServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的长期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的长期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *根据UID生成的长期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 expireDate = 2;
    public static final int EXPIREDATE_FIELD_NUMBER = 2;
    private long expireDate_;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    public boolean hasExpireDate() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    public long getExpireDate() {
      return expireDate_;
    }

    // optional int32 errorCode = 3;
    public static final int ERRORCODE_FIELD_NUMBER = 3;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 3;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 errorCode = 3;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    public int getErrorCode() {
      return errorCode_;
    }

    // optional string uid = 4;
    public static final int UID_FIELD_NUMBER = 4;
    private java.lang.Object uid_;
    /**
     * <code>optional string uid = 4;</code>
     *
     * <pre>
     *根据code或者token解析出来的uid
     * </pre>
     */
    public boolean hasUid() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string uid = 4;</code>
     *
     * <pre>
     *根据code或者token解析出来的uid
     * </pre>
     */
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          uid_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string uid = 4;</code>
     *
     * <pre>
     *根据code或者token解析出来的uid
     * </pre>
     */
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      expireDate_ = 0L;
      errorCode_ = 0;
      uid_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, expireDate_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, errorCode_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getUidBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireDate_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, errorCode_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getUidBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ServerToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        expireDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        uid_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.expireDate_ = expireDate_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.errorCode_ = errorCode_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.uid_ = uid_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasExpireDate()) {
          setExpireDate(other.getExpireDate());
        }
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        if (other.hasUid()) {
          bitField0_ |= 0x00000008;
          uid_ = other.uid_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的长期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的长期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的长期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的长期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的长期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *根据UID生成的长期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional int64 expireDate = 2;
      private long expireDate_ ;
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public boolean hasExpireDate() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public long getExpireDate() {
        return expireDate_;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder setExpireDate(long value) {
        bitField0_ |= 0x00000002;
        expireDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder clearExpireDate() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireDate_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 errorCode = 3;
      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 3;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 errorCode = 3;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 3;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000004;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 3;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000004);
        errorCode_ = 0;
        onChanged();
        return this;
      }

      // optional string uid = 4;
      private java.lang.Object uid_ = "";
      /**
       * <code>optional string uid = 4;</code>
       *
       * <pre>
       *根据code或者token解析出来的uid
       * </pre>
       */
      public boolean hasUid() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string uid = 4;</code>
       *
       * <pre>
       *根据code或者token解析出来的uid
       * </pre>
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string uid = 4;</code>
       *
       * <pre>
       *根据code或者token解析出来的uid
       * </pre>
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string uid = 4;</code>
       *
       * <pre>
       *根据code或者token解析出来的uid
       * </pre>
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string uid = 4;</code>
       *
       * <pre>
       *根据code或者token解析出来的uid
       * </pre>
       */
      public Builder clearUid() {
        bitField0_ = (bitField0_ & ~0x00000008);
        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <code>optional string uid = 4;</code>
       *
       * <pre>
       *根据code或者token解析出来的uid
       * </pre>
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        uid_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ServerToken)
    }

    static {
      defaultInstance = new ServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ServerToken)
  }

  public interface GameUserOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户ID
     * </pre>
     */
    long getUserId();

    // optional string gameUserId = 2;
    /**
     * <code>optional string gameUserId = 2;</code>
     *
     * <pre>
     *游戏用户ID
     * </pre>
     */
    boolean hasGameUserId();
    /**
     * <code>optional string gameUserId = 2;</code>
     *
     * <pre>
     *游戏用户ID
     * </pre>
     */
    java.lang.String getGameUserId();
    /**
     * <code>optional string gameUserId = 2;</code>
     *
     * <pre>
     *游戏用户ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameUserIdBytes();

    // optional string appId = 3;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *所属appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *所属appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *所属appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional int32 errorCode = 4;
    /**
     * <code>optional int32 errorCode = 4;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 4;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    int getErrorCode();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameUser}
   */
  public static final class GameUser extends
      com.google.protobuf.GeneratedMessage
      implements GameUserOrBuilder {
    // Use GameUser.newBuilder() to construct.
    private GameUser(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameUser(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameUser defaultInstance;
    public static GameUser getDefaultInstance() {
      return defaultInstance;
    }

    public GameUser getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameUser(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameUserId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appId_ = input.readBytes();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              errorCode_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder.class);
    }

    public static com.google.protobuf.Parser<GameUser> PARSER =
        new com.google.protobuf.AbstractParser<GameUser>() {
      public GameUser parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameUser(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameUser> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional string gameUserId = 2;
    public static final int GAMEUSERID_FIELD_NUMBER = 2;
    private java.lang.Object gameUserId_;
    /**
     * <code>optional string gameUserId = 2;</code>
     *
     * <pre>
     *游戏用户ID
     * </pre>
     */
    public boolean hasGameUserId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string gameUserId = 2;</code>
     *
     * <pre>
     *游戏用户ID
     * </pre>
     */
    public java.lang.String getGameUserId() {
      java.lang.Object ref = gameUserId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameUserId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string gameUserId = 2;</code>
     *
     * <pre>
     *游戏用户ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameUserIdBytes() {
      java.lang.Object ref = gameUserId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameUserId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 3;
    public static final int APPID_FIELD_NUMBER = 3;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *所属appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *所属appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *所属appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 errorCode = 4;
    public static final int ERRORCODE_FIELD_NUMBER = 4;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 4;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 errorCode = 4;</code>
     *
     * <pre>
     *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
     * </pre>
     */
    public int getErrorCode() {
      return errorCode_;
    }

    private void initFields() {
      userId_ = 0L;
      gameUserId_ = "";
      appId_ = "";
      errorCode_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameUserIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, errorCode_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameUserIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, errorCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameUser}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        gameUserId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameUserId_ = gameUserId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.errorCode_ = errorCode_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasGameUserId()) {
          bitField0_ |= 0x00000002;
          gameUserId_ = other.gameUserId_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000004;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional string gameUserId = 2;
      private java.lang.Object gameUserId_ = "";
      /**
       * <code>optional string gameUserId = 2;</code>
       *
       * <pre>
       *游戏用户ID
       * </pre>
       */
      public boolean hasGameUserId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string gameUserId = 2;</code>
       *
       * <pre>
       *游戏用户ID
       * </pre>
       */
      public java.lang.String getGameUserId() {
        java.lang.Object ref = gameUserId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameUserId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string gameUserId = 2;</code>
       *
       * <pre>
       *游戏用户ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameUserIdBytes() {
        java.lang.Object ref = gameUserId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameUserId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string gameUserId = 2;</code>
       *
       * <pre>
       *游戏用户ID
       * </pre>
       */
      public Builder setGameUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameUserId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string gameUserId = 2;</code>
       *
       * <pre>
       *游戏用户ID
       * </pre>
       */
      public Builder clearGameUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameUserId_ = getDefaultInstance().getGameUserId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string gameUserId = 2;</code>
       *
       * <pre>
       *游戏用户ID
       * </pre>
       */
      public Builder setGameUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameUserId_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 3;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *所属appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *所属appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *所属appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *所属appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *所属appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *所属appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional int32 errorCode = 4;
      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 4;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 errorCode = 4;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 4;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000008;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 4;</code>
       *
       * <pre>
       *服务端sdk错误码，isSuccess为false时返回，需透传到游戏服务器
       * </pre>
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000008);
        errorCode_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameUser)
    }

    static {
      defaultInstance = new GameUser(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameUser)
  }

  public interface RequestGetLoginTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    long getUserId();

    // optional string appId = 2;
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional string channel = 3;
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetLoginToken}
   *
   * <pre>
   * GameAuthService.java
   * 获取短期令牌，默认时长2小时
   * domain = 4302, op = 1
   * </pre>
   */
  public static final class RequestGetLoginToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetLoginTokenOrBuilder {
    // Use RequestGetLoginToken.newBuilder() to construct.
    private RequestGetLoginToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetLoginToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetLoginToken defaultInstance;
    public static RequestGetLoginToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetLoginToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetLoginToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channel_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetLoginToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetLoginToken>() {
      public RequestGetLoginToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetLoginToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetLoginToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 3;
    public static final int CHANNEL_FIELD_NUMBER = 3;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      userId_ = 0L;
      appId_ = "";
      channel_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetLoginToken}
     *
     * <pre>
     * GameAuthService.java
     * 获取短期令牌，默认时长2小时
     * domain = 4302, op = 1
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channel_ = channel_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000004;
          channel_ = other.channel_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 3;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetLoginToken)
    }

    static {
      defaultInstance = new RequestGetLoginToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetLoginToken)
  }

  public interface ResponseGetLoginTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken getToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder getTokenOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetLoginToken}
   */
  public static final class ResponseGetLoginToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetLoginTokenOrBuilder {
    // Use ResponseGetLoginToken.newBuilder() to construct.
    private ResponseGetLoginToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetLoginToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetLoginToken defaultInstance;
    public static ResponseGetLoginToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetLoginToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetLoginToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = token_.toBuilder();
              }
              token_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(token_);
                token_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetLoginToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetLoginToken>() {
      public ResponseGetLoginToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetLoginToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetLoginToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken token_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken getToken() {
      return token_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder getTokenOrBuilder() {
      return token_;
    }

    private void initFields() {
      token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetLoginToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTokenFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (tokenBuilder_ == null) {
          result.token_ = token_;
        } else {
          result.token_ = tokenBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          mergeToken(other.getToken());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;
      private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder> tokenBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken getToken() {
        if (tokenBuilder_ == null) {
          return token_;
        } else {
          return tokenBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public Builder setToken(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken value) {
        if (tokenBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          token_ = value;
          onChanged();
        } else {
          tokenBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public Builder setToken(
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder builderForValue) {
        if (tokenBuilder_ == null) {
          token_ = builderForValue.build();
          onChanged();
        } else {
          tokenBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public Builder mergeToken(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken value) {
        if (tokenBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              token_ != fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance()) {
            token_ =
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.newBuilder(token_).mergeFrom(value).buildPartial();
          } else {
            token_ = value;
          }
          onChanged();
        } else {
          tokenBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public Builder clearToken() {
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.getDefaultInstance();
          onChanged();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder getTokenBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTokenFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder getTokenOrBuilder() {
        if (tokenBuilder_ != null) {
          return tokenBuilder_.getMessageOrBuilder();
        } else {
          return token_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.LoginToken token = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder> 
          getTokenFieldBuilder() {
        if (tokenBuilder_ == null) {
          tokenBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginTokenOrBuilder>(
                  token_,
                  getParentForChildren(),
                  isClean());
          token_ = null;
        }
        return tokenBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetLoginToken)
    }

    static {
      defaultInstance = new ResponseGetLoginToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetLoginToken)
  }

  public interface RequestGetServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string loginToken = 1;
    /**
     * <code>optional string loginToken = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    boolean hasLoginToken();
    /**
     * <code>optional string loginToken = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    java.lang.String getLoginToken();
    /**
     * <code>optional string loginToken = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getLoginTokenBytes();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string appId = 3;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetServerToken}
   *
   * <pre>
   * GameAuthService.java
   * 获取长期令牌SSToken，SSToken为小游戏服务端与业务服务端数据交换的令牌
   * domain = 4302, op = 2
   * </pre>
   */
  public static final class RequestGetServerToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetServerTokenOrBuilder {
    // Use RequestGetServerToken.newBuilder() to construct.
    private RequestGetServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetServerToken defaultInstance;
    public static RequestGetServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              loginToken_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetServerToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetServerToken>() {
      public RequestGetServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string loginToken = 1;
    public static final int LOGINTOKEN_FIELD_NUMBER = 1;
    private java.lang.Object loginToken_;
    /**
     * <code>optional string loginToken = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    public boolean hasLoginToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string loginToken = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    public java.lang.String getLoginToken() {
      java.lang.Object ref = loginToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          loginToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string loginToken = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getLoginTokenBytes() {
      java.lang.Object ref = loginToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        loginToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 3;
    public static final int APPID_FIELD_NUMBER = 3;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      loginToken_ = "";
      channel_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getLoginTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getLoginTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetServerToken}
     *
     * <pre>
     * GameAuthService.java
     * 获取长期令牌SSToken，SSToken为小游戏服务端与业务服务端数据交换的令牌
     * domain = 4302, op = 2
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        loginToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.loginToken_ = loginToken_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken.getDefaultInstance()) return this;
        if (other.hasLoginToken()) {
          bitField0_ |= 0x00000001;
          loginToken_ = other.loginToken_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000004;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string loginToken = 1;
      private java.lang.Object loginToken_ = "";
      /**
       * <code>optional string loginToken = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public boolean hasLoginToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string loginToken = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public java.lang.String getLoginToken() {
        java.lang.Object ref = loginToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          loginToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string loginToken = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getLoginTokenBytes() {
        java.lang.Object ref = loginToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          loginToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string loginToken = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public Builder setLoginToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        loginToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string loginToken = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public Builder clearLoginToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        loginToken_ = getDefaultInstance().getLoginToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string loginToken = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public Builder setLoginTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        loginToken_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 3;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetServerToken)
    }

    static {
      defaultInstance = new RequestGetServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetServerToken)
  }

  public interface ResponseGetServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder getTokenOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetServerToken}
   */
  public static final class ResponseGetServerToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetServerTokenOrBuilder {
    // Use ResponseGetServerToken.newBuilder() to construct.
    private ResponseGetServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetServerToken defaultInstance;
    public static ResponseGetServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = token_.toBuilder();
              }
              token_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(token_);
                token_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetServerToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetServerToken>() {
      public ResponseGetServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken token_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getToken() {
      return token_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder getTokenOrBuilder() {
      return token_;
    }

    private void initFields() {
      token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetServerToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTokenFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (tokenBuilder_ == null) {
          result.token_ = token_;
        } else {
          result.token_ = tokenBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          mergeToken(other.getToken());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;
      private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder> tokenBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getToken() {
        if (tokenBuilder_ == null) {
          return token_;
        } else {
          return tokenBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder setToken(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken value) {
        if (tokenBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          token_ = value;
          onChanged();
        } else {
          tokenBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder setToken(
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder builderForValue) {
        if (tokenBuilder_ == null) {
          token_ = builderForValue.build();
          onChanged();
        } else {
          tokenBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder mergeToken(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken value) {
        if (tokenBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              token_ != fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance()) {
            token_ =
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.newBuilder(token_).mergeFrom(value).buildPartial();
          } else {
            token_ = value;
          }
          onChanged();
        } else {
          tokenBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder clearToken() {
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
          onChanged();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder getTokenBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTokenFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder getTokenOrBuilder() {
        if (tokenBuilder_ != null) {
          return tokenBuilder_.getMessageOrBuilder();
        } else {
          return token_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder> 
          getTokenFieldBuilder() {
        if (tokenBuilder_ == null) {
          tokenBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder>(
                  token_,
                  getParentForChildren(),
                  isClean());
          token_ = null;
        }
        return tokenBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetServerToken)
    }

    static {
      defaultInstance = new ResponseGetServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetServerToken)
  }

  public interface RequestUpdateServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string appId = 3;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestUpdateServerToken}
   *
   * <pre>
   * GameAuthService.java
   * 更新并返回长期令牌
   * domain = 4302, op = 3
   * </pre>
   */
  public static final class RequestUpdateServerToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestUpdateServerTokenOrBuilder {
    // Use RequestUpdateServerToken.newBuilder() to construct.
    private RequestUpdateServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestUpdateServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestUpdateServerToken defaultInstance;
    public static RequestUpdateServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestUpdateServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestUpdateServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestUpdateServerToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestUpdateServerToken>() {
      public RequestUpdateServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestUpdateServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestUpdateServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 3;
    public static final int APPID_FIELD_NUMBER = 3;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      channel_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestUpdateServerToken}
     *
     * <pre>
     * GameAuthService.java
     * 更新并返回长期令牌
     * domain = 4302, op = 3
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000004;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 3;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestUpdateServerToken)
    }

    static {
      defaultInstance = new RequestUpdateServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestUpdateServerToken)
  }

  public interface ResponseUpdateServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder getTokenOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateServerToken}
   */
  public static final class ResponseUpdateServerToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseUpdateServerTokenOrBuilder {
    // Use ResponseUpdateServerToken.newBuilder() to construct.
    private ResponseUpdateServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseUpdateServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseUpdateServerToken defaultInstance;
    public static ResponseUpdateServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseUpdateServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseUpdateServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = token_.toBuilder();
              }
              token_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(token_);
                token_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseUpdateServerToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseUpdateServerToken>() {
      public ResponseUpdateServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseUpdateServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseUpdateServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken token_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getToken() {
      return token_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder getTokenOrBuilder() {
      return token_;
    }

    private void initFields() {
      token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateServerToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTokenFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (tokenBuilder_ == null) {
          result.token_ = token_;
        } else {
          result.token_ = tokenBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          mergeToken(other.getToken());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;
      private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder> tokenBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken getToken() {
        if (tokenBuilder_ == null) {
          return token_;
        } else {
          return tokenBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder setToken(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken value) {
        if (tokenBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          token_ = value;
          onChanged();
        } else {
          tokenBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder setToken(
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder builderForValue) {
        if (tokenBuilder_ == null) {
          token_ = builderForValue.build();
          onChanged();
        } else {
          tokenBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder mergeToken(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken value) {
        if (tokenBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              token_ != fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance()) {
            token_ =
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.newBuilder(token_).mergeFrom(value).buildPartial();
          } else {
            token_ = value;
          }
          onChanged();
        } else {
          tokenBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public Builder clearToken() {
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.getDefaultInstance();
          onChanged();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder getTokenBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTokenFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder getTokenOrBuilder() {
        if (tokenBuilder_ != null) {
          return tokenBuilder_.getMessageOrBuilder();
        } else {
          return token_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.ServerToken token = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder> 
          getTokenFieldBuilder() {
        if (tokenBuilder_ == null) {
          tokenBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerTokenOrBuilder>(
                  token_,
                  getParentForChildren(),
                  isClean());
          token_ = null;
        }
        return tokenBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateServerToken)
    }

    static {
      defaultInstance = new ResponseUpdateServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseUpdateServerToken)
  }

  public interface RequestGetUserByServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string appId = 3;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetUserByServerToken}
   *
   * <pre>
   * GameAuthService.java
   * 通过长期令牌获得用户
   * domain = 4302, op = 4
   * </pre>
   */
  public static final class RequestGetUserByServerToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetUserByServerTokenOrBuilder {
    // Use RequestGetUserByServerToken.newBuilder() to construct.
    private RequestGetUserByServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetUserByServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetUserByServerToken defaultInstance;
    public static RequestGetUserByServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetUserByServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetUserByServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetUserByServerToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetUserByServerToken>() {
      public RequestGetUserByServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetUserByServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetUserByServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 3;
    public static final int APPID_FIELD_NUMBER = 3;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      channel_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetUserByServerToken}
     *
     * <pre>
     * GameAuthService.java
     * 通过长期令牌获得用户
     * domain = 4302, op = 4
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000004;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 3;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetUserByServerToken)
    }

    static {
      defaultInstance = new RequestGetUserByServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetUserByServerToken)
  }

  public interface ResponseGetUserByServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
     */
    boolean hasGameUser();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser getGameUser();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder getGameUserOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserByServerToken}
   */
  public static final class ResponseGetUserByServerToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetUserByServerTokenOrBuilder {
    // Use ResponseGetUserByServerToken.newBuilder() to construct.
    private ResponseGetUserByServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetUserByServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetUserByServerToken defaultInstance;
    public static ResponseGetUserByServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetUserByServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetUserByServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = gameUser_.toBuilder();
              }
              gameUser_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(gameUser_);
                gameUser_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetUserByServerToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetUserByServerToken>() {
      public ResponseGetUserByServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetUserByServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetUserByServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;
    public static final int GAMEUSER_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser gameUser_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
     */
    public boolean hasGameUser() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser getGameUser() {
      return gameUser_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder getGameUserOrBuilder() {
      return gameUser_;
    }

    private void initFields() {
      gameUser_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, gameUser_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameUser_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserByServerToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameUserFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameUserBuilder_ == null) {
          gameUser_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance();
        } else {
          gameUserBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (gameUserBuilder_ == null) {
          result.gameUser_ = gameUser_;
        } else {
          result.gameUser_ = gameUserBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken.getDefaultInstance()) return this;
        if (other.hasGameUser()) {
          mergeGameUser(other.getGameUser());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;
      private fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser gameUser_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder> gameUserBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public boolean hasGameUser() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser getGameUser() {
        if (gameUserBuilder_ == null) {
          return gameUser_;
        } else {
          return gameUserBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public Builder setGameUser(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser value) {
        if (gameUserBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gameUser_ = value;
          onChanged();
        } else {
          gameUserBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public Builder setGameUser(
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder builderForValue) {
        if (gameUserBuilder_ == null) {
          gameUser_ = builderForValue.build();
          onChanged();
        } else {
          gameUserBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public Builder mergeGameUser(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser value) {
        if (gameUserBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              gameUser_ != fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance()) {
            gameUser_ =
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.newBuilder(gameUser_).mergeFrom(value).buildPartial();
          } else {
            gameUser_ = value;
          }
          onChanged();
        } else {
          gameUserBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public Builder clearGameUser() {
        if (gameUserBuilder_ == null) {
          gameUser_ = fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.getDefaultInstance();
          onChanged();
        } else {
          gameUserBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder getGameUserBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getGameUserFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder getGameUserOrBuilder() {
        if (gameUserBuilder_ != null) {
          return gameUserBuilder_.getMessageOrBuilder();
        } else {
          return gameUser_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameUser gameUser = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder> 
          getGameUserFieldBuilder() {
        if (gameUserBuilder_ == null) {
          gameUserBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser.Builder, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUserOrBuilder>(
                  gameUser_,
                  getParentForChildren(),
                  isClean());
          gameUser_ = null;
        }
        return gameUserBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserByServerToken)
    }

    static {
      defaultInstance = new ResponseGetUserByServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserByServerToken)
  }

  public interface RequestVerifyLoginTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string appId = 3;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestVerifyLoginToken}
   *
   * <pre>
   * GameAuthService.java
   * 校验短期令牌有效性
   * domain = 4302, op = 5
   * </pre>
   */
  public static final class RequestVerifyLoginToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestVerifyLoginTokenOrBuilder {
    // Use RequestVerifyLoginToken.newBuilder() to construct.
    private RequestVerifyLoginToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestVerifyLoginToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestVerifyLoginToken defaultInstance;
    public static RequestVerifyLoginToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestVerifyLoginToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestVerifyLoginToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestVerifyLoginToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestVerifyLoginToken>() {
      public RequestVerifyLoginToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestVerifyLoginToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestVerifyLoginToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *短期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 3;
    public static final int APPID_FIELD_NUMBER = 3;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      channel_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestVerifyLoginToken}
     *
     * <pre>
     * GameAuthService.java
     * 校验短期令牌有效性
     * domain = 4302, op = 5
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000004;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *短期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 3;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestVerifyLoginToken)
    }

    static {
      defaultInstance = new RequestVerifyLoginToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestVerifyLoginToken)
  }

  public interface ResponseVerifyLoginTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int32 errorCode = 1;
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    int getErrorCode();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyLoginToken}
   */
  public static final class ResponseVerifyLoginToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseVerifyLoginTokenOrBuilder {
    // Use ResponseVerifyLoginToken.newBuilder() to construct.
    private ResponseVerifyLoginToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseVerifyLoginToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseVerifyLoginToken defaultInstance;
    public static ResponseVerifyLoginToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseVerifyLoginToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseVerifyLoginToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorCode_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseVerifyLoginToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseVerifyLoginToken>() {
      public ResponseVerifyLoginToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseVerifyLoginToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseVerifyLoginToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int32 errorCode = 1;
    public static final int ERRORCODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    public int getErrorCode() {
      return errorCode_;
    }

    private void initFields() {
      errorCode_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorCode_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyLoginToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorCode_ = errorCode_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken.getDefaultInstance()) return this;
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int32 errorCode = 1;
      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000001;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyLoginToken)
    }

    static {
      defaultInstance = new ResponseVerifyLoginToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyLoginToken)
  }

  public interface RequestVerifyServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string appId = 3;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestVerifyServerToken}
   *
   * <pre>
   * GameAuthService.java
   * 校验长期令牌有效性
   * domain = 4302, op = 6
   * </pre>
   */
  public static final class RequestVerifyServerToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestVerifyServerTokenOrBuilder {
    // Use RequestVerifyServerToken.newBuilder() to construct.
    private RequestVerifyServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestVerifyServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestVerifyServerToken defaultInstance;
    public static RequestVerifyServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestVerifyServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestVerifyServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestVerifyServerToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestVerifyServerToken>() {
      public RequestVerifyServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestVerifyServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestVerifyServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 3;
    public static final int APPID_FIELD_NUMBER = 3;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 3;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      channel_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestVerifyServerToken}
     *
     * <pre>
     * GameAuthService.java
     * 校验长期令牌有效性
     * domain = 4302, op = 6
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000004;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 3;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 3;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestVerifyServerToken)
    }

    static {
      defaultInstance = new RequestVerifyServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestVerifyServerToken)
  }

  public interface ResponseVerifyServerTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int32 errorCode = 1;
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    int getErrorCode();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyServerToken}
   */
  public static final class ResponseVerifyServerToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseVerifyServerTokenOrBuilder {
    // Use ResponseVerifyServerToken.newBuilder() to construct.
    private ResponseVerifyServerToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseVerifyServerToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseVerifyServerToken defaultInstance;
    public static ResponseVerifyServerToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseVerifyServerToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseVerifyServerToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorCode_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseVerifyServerToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseVerifyServerToken>() {
      public ResponseVerifyServerToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseVerifyServerToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseVerifyServerToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int32 errorCode = 1;
    public static final int ERRORCODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 errorCode = 1;</code>
     */
    public int getErrorCode() {
      return errorCode_;
    }

    private void initFields() {
      errorCode_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorCode_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyServerToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.class, fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken build() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken result = new fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorCode_ = errorCode_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken.getDefaultInstance()) return this;
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int32 errorCode = 1;
      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000001;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyServerToken)
    }

    static {
      defaultInstance = new ResponseVerifyServerToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseVerifyServerToken)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030protocol_game_auth.proto\022-fm.lizhi.com" +
      "mons.template.datacenter.protocol\"/\n\nLog" +
      "inToken\022\r\n\005token\030\001 \001(\t\022\022\n\nexpireDate\030\002 \001" +
      "(\003\"P\n\013ServerToken\022\r\n\005token\030\001 \001(\t\022\022\n\nexpi" +
      "reDate\030\002 \001(\003\022\021\n\terrorCode\030\003 \001(\005\022\013\n\003uid\030\004" +
      " \001(\t\"P\n\010GameUser\022\016\n\006userId\030\001 \001(\003\022\022\n\ngame" +
      "UserId\030\002 \001(\t\022\r\n\005appId\030\003 \001(\t\022\021\n\terrorCode" +
      "\030\004 \001(\005\"F\n\024RequestGetLoginToken\022\016\n\006userId" +
      "\030\001 \001(\003\022\r\n\005appId\030\002 \001(\t\022\017\n\007channel\030\003 \001(\t\"a" +
      "\n\025ResponseGetLoginToken\022H\n\005token\030\001 \001(\01329",
      ".fm.lizhi.commons.template.datacenter.pr" +
      "otocol.LoginToken\"K\n\025RequestGetServerTok" +
      "en\022\022\n\nloginToken\030\001 \001(\t\022\017\n\007channel\030\002 \001(\t\022" +
      "\r\n\005appId\030\003 \001(\t\"c\n\026ResponseGetServerToken" +
      "\022I\n\005token\030\001 \001(\0132:.fm.lizhi.commons.templ" +
      "ate.datacenter.protocol.ServerToken\"I\n\030R" +
      "equestUpdateServerToken\022\r\n\005token\030\001 \001(\t\022\017" +
      "\n\007channel\030\002 \001(\t\022\r\n\005appId\030\003 \001(\t\"f\n\031Respon" +
      "seUpdateServerToken\022I\n\005token\030\001 \001(\0132:.fm." +
      "lizhi.commons.template.datacenter.protoc",
      "ol.ServerToken\"L\n\033RequestGetUserByServer" +
      "Token\022\r\n\005token\030\001 \001(\t\022\017\n\007channel\030\002 \001(\t\022\r\n" +
      "\005appId\030\003 \001(\t\"i\n\034ResponseGetUserByServerT" +
      "oken\022I\n\010gameUser\030\001 \001(\01327.fm.lizhi.common" +
      "s.template.datacenter.protocol.GameUser\"" +
      "H\n\027RequestVerifyLoginToken\022\r\n\005token\030\001 \001(" +
      "\t\022\017\n\007channel\030\002 \001(\t\022\r\n\005appId\030\003 \001(\t\"-\n\030Res" +
      "ponseVerifyLoginToken\022\021\n\terrorCode\030\001 \001(\005" +
      "\"I\n\030RequestVerifyServerToken\022\r\n\005token\030\001 " +
      "\001(\t\022\017\n\007channel\030\002 \001(\t\022\r\n\005appId\030\003 \001(\t\".\n\031R",
      "esponseVerifyServerToken\022\021\n\terrorCode\030\001 " +
      "\001(\005B6\n\034fm.lizhi.ocean.seal.protocolB\024Gam" +
      "eAuthServiceProtoH\001"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_LoginToken_descriptor,
              new java.lang.String[] { "Token", "ExpireDate", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ServerToken_descriptor,
              new java.lang.String[] { "Token", "ExpireDate", "ErrorCode", "Uid", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameUser_descriptor,
              new java.lang.String[] { "UserId", "GameUserId", "AppId", "ErrorCode", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetLoginToken_descriptor,
              new java.lang.String[] { "UserId", "AppId", "Channel", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetLoginToken_descriptor,
              new java.lang.String[] { "Token", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetServerToken_descriptor,
              new java.lang.String[] { "LoginToken", "Channel", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetServerToken_descriptor,
              new java.lang.String[] { "Token", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestUpdateServerToken_descriptor,
              new java.lang.String[] { "Token", "Channel", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseUpdateServerToken_descriptor,
              new java.lang.String[] { "Token", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserByServerToken_descriptor,
              new java.lang.String[] { "Token", "Channel", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_descriptor =
            getDescriptor().getMessageTypes().get(10);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserByServerToken_descriptor,
              new java.lang.String[] { "GameUser", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_descriptor =
            getDescriptor().getMessageTypes().get(11);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyLoginToken_descriptor,
              new java.lang.String[] { "Token", "Channel", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_descriptor =
            getDescriptor().getMessageTypes().get(12);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyLoginToken_descriptor,
              new java.lang.String[] { "ErrorCode", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_descriptor =
            getDescriptor().getMessageTypes().get(13);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifyServerToken_descriptor,
              new java.lang.String[] { "Token", "Channel", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_descriptor =
            getDescriptor().getMessageTypes().get(14);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifyServerToken_descriptor,
              new java.lang.String[] { "ErrorCode", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
