package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.mapper.GameCallbackMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@AutoBindSingleton
public class GameCallbackManager {
    @Inject
    private GameCallbackMapper gameCallbackMapper;
    @Inject
    private GuidGenerator guidGenerator;

    /**
     * 根据appId获取回调信息
     *
     * @param appId appID
     * @param callbackKey callbackKey
     * @param type  回调类型
     * @return
     */
    public GameCallback getGameCallback(String appId, String callbackKey, int type, String eventName) {
        GameCallback gameCallback = new GameCallback();
        gameCallback.setAppId(appId);
        gameCallback.setType(type);
        if(StringUtils.isNotBlank(callbackKey)){
            gameCallback.setCallbackKey(callbackKey);
        }
        if (StringUtils.isNoneBlank(eventName)) {
            gameCallback.setEventName(eventName);
        }
        List<GameCallback> gameCallbacks = this.gameCallbackMapper.selectMany(gameCallback);
        if (gameCallbacks == null || gameCallbacks.isEmpty()) {
            return null;
        }
        return gameCallbacks.get(0);
    }

    /**
     *
     * @param id
     * @param appId
     * @param callbackKey
     * @param type //回调类型，1：用户信息回调 2 回调业务地址 3 游戏接口地址 12 线上业务地址 13线上接口地址
     * @param url
     */
    public void insertOrUpdate(long id, String appId, String callbackKey, int type, String url){
        Date date = new Date();
        boolean insert = id <= 0L;
        GameCallback bean = new GameCallback();
        if(insert){
            bean.setId(guidGenerator.genId());
            bean.setCreateTime(date);
        }else {
            bean = getGameCallbackById(id);
            if (null == bean){
                return;
            }
        }

        bean.setAppId(appId);
        bean.setCallbackKey(Optional.ofNullable(callbackKey).orElse(StringUtils.EMPTY));
        bean.setType(type);
        bean.setUrl(url);
        bean.setModifyTime(date);
        if(insert){
            gameCallbackMapper.insert(bean);
        }else {
            gameCallbackMapper.updateByPrimaryKey(bean);
        }

    }

    public void delCallback(long id){
        GameCallback bean = new GameCallback();
        bean.setId(id);
        gameCallbackMapper.deleteByPrimaryKey(bean);
    }

    public GameCallback getGameCallbackById(long id){
        GameCallback bean = new GameCallback();
        bean.setId(id);
        return gameCallbackMapper.selectByPrimaryKey(bean);
    }

    public PageList<GameCallback> pageSearch(String appId, int pageSize, int pageNumber){
        GameCallback bean = new GameCallback();
        if(StringUtils.isNotBlank(appId)){
            bean.setAppId(appId);
        }
        return gameCallbackMapper.selectPage(bean, pageNumber, pageSize);
    }

}
