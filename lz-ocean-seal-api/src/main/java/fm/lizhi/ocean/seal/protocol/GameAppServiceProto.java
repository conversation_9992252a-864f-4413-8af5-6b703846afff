// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_app.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameAppServiceProto {
  private GameAppServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GetAppCallbackParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional int32 type = 2;
    /**
     * <code>optional int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    int getType();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam}
   *
   * <pre>
   *获取回调配置信息参数
   * </pre>
   */
  public static final class GetAppCallbackParam extends
      com.google.protobuf.GeneratedMessage
      implements GetAppCallbackParamOrBuilder {
    // Use GetAppCallbackParam.newBuilder() to construct.
    private GetAppCallbackParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetAppCallbackParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetAppCallbackParam defaultInstance;
    public static GetAppCallbackParam getDefaultInstance() {
      return defaultInstance;
    }

    public GetAppCallbackParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetAppCallbackParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              type_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GetAppCallbackParam> PARSER =
        new com.google.protobuf.AbstractParser<GetAppCallbackParam>() {
      public GetAppCallbackParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetAppCallbackParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetAppCallbackParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 type = 2;
    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>optional int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    public int getType() {
      return type_;
    }

    private void initFields() {
      appId_ = "";
      type_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, type_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam}
     *
     * <pre>
     *获取回调配置信息参数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional int32 type = 2;
      private int type_ ;
      /**
       * <code>optional int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000002;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam)
    }

    static {
      defaultInstance = new GetAppCallbackParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam)
  }

  public interface AppCallbackOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required int32 type = 2;
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    int getType();

    // required string callbackKey = 3;
    /**
     * <code>required string callbackKey = 3;</code>
     *
     * <pre>
     * 接口回调Key
     * </pre>
     */
    boolean hasCallbackKey();
    /**
     * <code>required string callbackKey = 3;</code>
     *
     * <pre>
     * 接口回调Key
     * </pre>
     */
    java.lang.String getCallbackKey();
    /**
     * <code>required string callbackKey = 3;</code>
     *
     * <pre>
     * 接口回调Key
     * </pre>
     */
    com.google.protobuf.ByteString
        getCallbackKeyBytes();

    // required string callbackGetUserUrl = 4;
    /**
     * <code>required string callbackGetUserUrl = 4;</code>
     *
     * <pre>
     * 获取用户信息地址
     * </pre>
     */
    boolean hasCallbackGetUserUrl();
    /**
     * <code>required string callbackGetUserUrl = 4;</code>
     *
     * <pre>
     * 获取用户信息地址
     * </pre>
     */
    java.lang.String getCallbackGetUserUrl();
    /**
     * <code>required string callbackGetUserUrl = 4;</code>
     *
     * <pre>
     * 获取用户信息地址
     * </pre>
     */
    com.google.protobuf.ByteString
        getCallbackGetUserUrlBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.AppCallback}
   *
   * <pre>
   *回调配置信息
   * </pre>
   */
  public static final class AppCallback extends
      com.google.protobuf.GeneratedMessage
      implements AppCallbackOrBuilder {
    // Use AppCallback.newBuilder() to construct.
    private AppCallback(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private AppCallback(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final AppCallback defaultInstance;
    public static AppCallback getDefaultInstance() {
      return defaultInstance;
    }

    public AppCallback getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private AppCallback(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              type_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              callbackKey_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              callbackGetUserUrl_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder.class);
    }

    public static com.google.protobuf.Parser<AppCallback> PARSER =
        new com.google.protobuf.AbstractParser<AppCallback>() {
      public AppCallback parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AppCallback(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<AppCallback> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 type = 2;
    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // required string callbackKey = 3;
    public static final int CALLBACKKEY_FIELD_NUMBER = 3;
    private java.lang.Object callbackKey_;
    /**
     * <code>required string callbackKey = 3;</code>
     *
     * <pre>
     * 接口回调Key
     * </pre>
     */
    public boolean hasCallbackKey() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string callbackKey = 3;</code>
     *
     * <pre>
     * 接口回调Key
     * </pre>
     */
    public java.lang.String getCallbackKey() {
      java.lang.Object ref = callbackKey_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          callbackKey_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string callbackKey = 3;</code>
     *
     * <pre>
     * 接口回调Key
     * </pre>
     */
    public com.google.protobuf.ByteString
        getCallbackKeyBytes() {
      java.lang.Object ref = callbackKey_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        callbackKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string callbackGetUserUrl = 4;
    public static final int CALLBACKGETUSERURL_FIELD_NUMBER = 4;
    private java.lang.Object callbackGetUserUrl_;
    /**
     * <code>required string callbackGetUserUrl = 4;</code>
     *
     * <pre>
     * 获取用户信息地址
     * </pre>
     */
    public boolean hasCallbackGetUserUrl() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string callbackGetUserUrl = 4;</code>
     *
     * <pre>
     * 获取用户信息地址
     * </pre>
     */
    public java.lang.String getCallbackGetUserUrl() {
      java.lang.Object ref = callbackGetUserUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          callbackGetUserUrl_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string callbackGetUserUrl = 4;</code>
     *
     * <pre>
     * 获取用户信息地址
     * </pre>
     */
    public com.google.protobuf.ByteString
        getCallbackGetUserUrlBytes() {
      java.lang.Object ref = callbackGetUserUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        callbackGetUserUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
      type_ = 0;
      callbackKey_ = "";
      callbackGetUserUrl_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCallbackKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCallbackGetUserUrl()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, type_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getCallbackKeyBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getCallbackGetUserUrlBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getCallbackKeyBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getCallbackGetUserUrlBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.AppCallback}
     *
     * <pre>
     *回调配置信息
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        callbackKey_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        callbackGetUserUrl_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.callbackKey_ = callbackKey_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.callbackGetUserUrl_ = callbackGetUserUrl_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasCallbackKey()) {
          bitField0_ |= 0x00000004;
          callbackKey_ = other.callbackKey_;
          onChanged();
        }
        if (other.hasCallbackGetUserUrl()) {
          bitField0_ |= 0x00000008;
          callbackGetUserUrl_ = other.callbackGetUserUrl_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasType()) {
          
          return false;
        }
        if (!hasCallbackKey()) {
          
          return false;
        }
        if (!hasCallbackGetUserUrl()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // required int32 type = 2;
      private int type_ ;
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000002;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       * 回调类型，详见：fm.lizhi.ocean.seal.constant.GameCallbackType
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      // required string callbackKey = 3;
      private java.lang.Object callbackKey_ = "";
      /**
       * <code>required string callbackKey = 3;</code>
       *
       * <pre>
       * 接口回调Key
       * </pre>
       */
      public boolean hasCallbackKey() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string callbackKey = 3;</code>
       *
       * <pre>
       * 接口回调Key
       * </pre>
       */
      public java.lang.String getCallbackKey() {
        java.lang.Object ref = callbackKey_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          callbackKey_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string callbackKey = 3;</code>
       *
       * <pre>
       * 接口回调Key
       * </pre>
       */
      public com.google.protobuf.ByteString
          getCallbackKeyBytes() {
        java.lang.Object ref = callbackKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          callbackKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string callbackKey = 3;</code>
       *
       * <pre>
       * 接口回调Key
       * </pre>
       */
      public Builder setCallbackKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        callbackKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string callbackKey = 3;</code>
       *
       * <pre>
       * 接口回调Key
       * </pre>
       */
      public Builder clearCallbackKey() {
        bitField0_ = (bitField0_ & ~0x00000004);
        callbackKey_ = getDefaultInstance().getCallbackKey();
        onChanged();
        return this;
      }
      /**
       * <code>required string callbackKey = 3;</code>
       *
       * <pre>
       * 接口回调Key
       * </pre>
       */
      public Builder setCallbackKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        callbackKey_ = value;
        onChanged();
        return this;
      }

      // required string callbackGetUserUrl = 4;
      private java.lang.Object callbackGetUserUrl_ = "";
      /**
       * <code>required string callbackGetUserUrl = 4;</code>
       *
       * <pre>
       * 获取用户信息地址
       * </pre>
       */
      public boolean hasCallbackGetUserUrl() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string callbackGetUserUrl = 4;</code>
       *
       * <pre>
       * 获取用户信息地址
       * </pre>
       */
      public java.lang.String getCallbackGetUserUrl() {
        java.lang.Object ref = callbackGetUserUrl_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          callbackGetUserUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string callbackGetUserUrl = 4;</code>
       *
       * <pre>
       * 获取用户信息地址
       * </pre>
       */
      public com.google.protobuf.ByteString
          getCallbackGetUserUrlBytes() {
        java.lang.Object ref = callbackGetUserUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          callbackGetUserUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string callbackGetUserUrl = 4;</code>
       *
       * <pre>
       * 获取用户信息地址
       * </pre>
       */
      public Builder setCallbackGetUserUrl(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        callbackGetUserUrl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string callbackGetUserUrl = 4;</code>
       *
       * <pre>
       * 获取用户信息地址
       * </pre>
       */
      public Builder clearCallbackGetUserUrl() {
        bitField0_ = (bitField0_ & ~0x00000008);
        callbackGetUserUrl_ = getDefaultInstance().getCallbackGetUserUrl();
        onChanged();
        return this;
      }
      /**
       * <code>required string callbackGetUserUrl = 4;</code>
       *
       * <pre>
       * 获取用户信息地址
       * </pre>
       */
      public Builder setCallbackGetUserUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        callbackGetUserUrl_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.AppCallback)
    }

    static {
      defaultInstance = new AppCallback(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.AppCallback)
  }

  public interface ChannelInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string channelAppId = 3;
    /**
     * <code>optional string channelAppId = 3;</code>
     */
    boolean hasChannelAppId();
    /**
     * <code>optional string channelAppId = 3;</code>
     */
    java.lang.String getChannelAppId();
    /**
     * <code>optional string channelAppId = 3;</code>
     */
    com.google.protobuf.ByteString
        getChannelAppIdBytes();

    // optional string channelAppKey = 4;
    /**
     * <code>optional string channelAppKey = 4;</code>
     */
    boolean hasChannelAppKey();
    /**
     * <code>optional string channelAppKey = 4;</code>
     */
    java.lang.String getChannelAppKey();
    /**
     * <code>optional string channelAppKey = 4;</code>
     */
    com.google.protobuf.ByteString
        getChannelAppKeyBytes();

    // optional string channelAppSecret = 5;
    /**
     * <code>optional string channelAppSecret = 5;</code>
     */
    boolean hasChannelAppSecret();
    /**
     * <code>optional string channelAppSecret = 5;</code>
     */
    java.lang.String getChannelAppSecret();
    /**
     * <code>optional string channelAppSecret = 5;</code>
     */
    com.google.protobuf.ByteString
        getChannelAppSecretBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ChannelInfo}
   */
  public static final class ChannelInfo extends
      com.google.protobuf.GeneratedMessage
      implements ChannelInfoOrBuilder {
    // Use ChannelInfo.newBuilder() to construct.
    private ChannelInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ChannelInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ChannelInfo defaultInstance;
    public static ChannelInfo getDefaultInstance() {
      return defaultInstance;
    }

    public ChannelInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ChannelInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channelAppId_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              channelAppKey_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              channelAppSecret_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<ChannelInfo> PARSER =
        new com.google.protobuf.AbstractParser<ChannelInfo>() {
      public ChannelInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChannelInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ChannelInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channelAppId = 3;
    public static final int CHANNELAPPID_FIELD_NUMBER = 3;
    private java.lang.Object channelAppId_;
    /**
     * <code>optional string channelAppId = 3;</code>
     */
    public boolean hasChannelAppId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string channelAppId = 3;</code>
     */
    public java.lang.String getChannelAppId() {
      java.lang.Object ref = channelAppId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelAppId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelAppId = 3;</code>
     */
    public com.google.protobuf.ByteString
        getChannelAppIdBytes() {
      java.lang.Object ref = channelAppId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelAppId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channelAppKey = 4;
    public static final int CHANNELAPPKEY_FIELD_NUMBER = 4;
    private java.lang.Object channelAppKey_;
    /**
     * <code>optional string channelAppKey = 4;</code>
     */
    public boolean hasChannelAppKey() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string channelAppKey = 4;</code>
     */
    public java.lang.String getChannelAppKey() {
      java.lang.Object ref = channelAppKey_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelAppKey_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelAppKey = 4;</code>
     */
    public com.google.protobuf.ByteString
        getChannelAppKeyBytes() {
      java.lang.Object ref = channelAppKey_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelAppKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channelAppSecret = 5;
    public static final int CHANNELAPPSECRET_FIELD_NUMBER = 5;
    private java.lang.Object channelAppSecret_;
    /**
     * <code>optional string channelAppSecret = 5;</code>
     */
    public boolean hasChannelAppSecret() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional string channelAppSecret = 5;</code>
     */
    public java.lang.String getChannelAppSecret() {
      java.lang.Object ref = channelAppSecret_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelAppSecret_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelAppSecret = 5;</code>
     */
    public com.google.protobuf.ByteString
        getChannelAppSecretBytes() {
      java.lang.Object ref = channelAppSecret_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelAppSecret_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      id_ = 0L;
      channel_ = "";
      channelAppId_ = "";
      channelAppKey_ = "";
      channelAppSecret_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelAppIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getChannelAppKeyBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getChannelAppSecretBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelAppIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getChannelAppKeyBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getChannelAppSecretBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ChannelInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        channelAppId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        channelAppKey_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        channelAppSecret_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelAppId_ = channelAppId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.channelAppKey_ = channelAppKey_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.channelAppSecret_ = channelAppSecret_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasChannelAppId()) {
          bitField0_ |= 0x00000004;
          channelAppId_ = other.channelAppId_;
          onChanged();
        }
        if (other.hasChannelAppKey()) {
          bitField0_ |= 0x00000008;
          channelAppKey_ = other.channelAppKey_;
          onChanged();
        }
        if (other.hasChannelAppSecret()) {
          bitField0_ |= 0x00000010;
          channelAppSecret_ = other.channelAppSecret_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string channelAppId = 3;
      private java.lang.Object channelAppId_ = "";
      /**
       * <code>optional string channelAppId = 3;</code>
       */
      public boolean hasChannelAppId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string channelAppId = 3;</code>
       */
      public java.lang.String getChannelAppId() {
        java.lang.Object ref = channelAppId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelAppId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelAppId = 3;</code>
       */
      public com.google.protobuf.ByteString
          getChannelAppIdBytes() {
        java.lang.Object ref = channelAppId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelAppId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelAppId = 3;</code>
       */
      public Builder setChannelAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelAppId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelAppId = 3;</code>
       */
      public Builder clearChannelAppId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelAppId_ = getDefaultInstance().getChannelAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelAppId = 3;</code>
       */
      public Builder setChannelAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelAppId_ = value;
        onChanged();
        return this;
      }

      // optional string channelAppKey = 4;
      private java.lang.Object channelAppKey_ = "";
      /**
       * <code>optional string channelAppKey = 4;</code>
       */
      public boolean hasChannelAppKey() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string channelAppKey = 4;</code>
       */
      public java.lang.String getChannelAppKey() {
        java.lang.Object ref = channelAppKey_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelAppKey_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelAppKey = 4;</code>
       */
      public com.google.protobuf.ByteString
          getChannelAppKeyBytes() {
        java.lang.Object ref = channelAppKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelAppKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelAppKey = 4;</code>
       */
      public Builder setChannelAppKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        channelAppKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelAppKey = 4;</code>
       */
      public Builder clearChannelAppKey() {
        bitField0_ = (bitField0_ & ~0x00000008);
        channelAppKey_ = getDefaultInstance().getChannelAppKey();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelAppKey = 4;</code>
       */
      public Builder setChannelAppKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        channelAppKey_ = value;
        onChanged();
        return this;
      }

      // optional string channelAppSecret = 5;
      private java.lang.Object channelAppSecret_ = "";
      /**
       * <code>optional string channelAppSecret = 5;</code>
       */
      public boolean hasChannelAppSecret() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional string channelAppSecret = 5;</code>
       */
      public java.lang.String getChannelAppSecret() {
        java.lang.Object ref = channelAppSecret_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelAppSecret_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelAppSecret = 5;</code>
       */
      public com.google.protobuf.ByteString
          getChannelAppSecretBytes() {
        java.lang.Object ref = channelAppSecret_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelAppSecret_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelAppSecret = 5;</code>
       */
      public Builder setChannelAppSecret(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        channelAppSecret_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelAppSecret = 5;</code>
       */
      public Builder clearChannelAppSecret() {
        bitField0_ = (bitField0_ & ~0x00000010);
        channelAppSecret_ = getDefaultInstance().getChannelAppSecret();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelAppSecret = 5;</code>
       */
      public Builder setChannelAppSecretBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        channelAppSecret_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ChannelInfo)
    }

    static {
      defaultInstance = new ChannelInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ChannelInfo)
  }

  public interface GameAppInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional string appId = 2;
    /**
     * <code>optional string appId = 2;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 2;</code>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 2;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional string appSecret = 3;
    /**
     * <code>optional string appSecret = 3;</code>
     */
    boolean hasAppSecret();
    /**
     * <code>optional string appSecret = 3;</code>
     */
    java.lang.String getAppSecret();
    /**
     * <code>optional string appSecret = 3;</code>
     */
    com.google.protobuf.ByteString
        getAppSecretBytes();

    // optional string appName = 4;
    /**
     * <code>optional string appName = 4;</code>
     */
    boolean hasAppName();
    /**
     * <code>optional string appName = 4;</code>
     */
    java.lang.String getAppName();
    /**
     * <code>optional string appName = 4;</code>
     */
    com.google.protobuf.ByteString
        getAppNameBytes();

    // optional string appAlias = 5;
    /**
     * <code>optional string appAlias = 5;</code>
     */
    boolean hasAppAlias();
    /**
     * <code>optional string appAlias = 5;</code>
     */
    java.lang.String getAppAlias();
    /**
     * <code>optional string appAlias = 5;</code>
     */
    com.google.protobuf.ByteString
        getAppAliasBytes();

    // optional string appTopic = 6;
    /**
     * <code>optional string appTopic = 6;</code>
     */
    boolean hasAppTopic();
    /**
     * <code>optional string appTopic = 6;</code>
     */
    java.lang.String getAppTopic();
    /**
     * <code>optional string appTopic = 6;</code>
     */
    com.google.protobuf.ByteString
        getAppTopicBytes();

    // repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo> 
        getChannelInfosList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo getChannelInfos(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    int getChannelInfosCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder> 
        getChannelInfosOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder getChannelInfosOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameAppInfo}
   *
   * <pre>
   *平台颁发给业务的信息表
   * </pre>
   */
  public static final class GameAppInfo extends
      com.google.protobuf.GeneratedMessage
      implements GameAppInfoOrBuilder {
    // Use GameAppInfo.newBuilder() to construct.
    private GameAppInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameAppInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameAppInfo defaultInstance;
    public static GameAppInfo getDefaultInstance() {
      return defaultInstance;
    }

    public GameAppInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameAppInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              appSecret_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              appName_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              appAlias_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              appTopic_ = input.readBytes();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
                channelInfos_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo>();
                mutable_bitField0_ |= 0x00000040;
              }
              channelInfos_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
          channelInfos_ = java.util.Collections.unmodifiableList(channelInfos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<GameAppInfo> PARSER =
        new com.google.protobuf.AbstractParser<GameAppInfo>() {
      public GameAppInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameAppInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameAppInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 2;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appId = 2;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appSecret = 3;
    public static final int APPSECRET_FIELD_NUMBER = 3;
    private java.lang.Object appSecret_;
    /**
     * <code>optional string appSecret = 3;</code>
     */
    public boolean hasAppSecret() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string appSecret = 3;</code>
     */
    public java.lang.String getAppSecret() {
      java.lang.Object ref = appSecret_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appSecret_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appSecret = 3;</code>
     */
    public com.google.protobuf.ByteString
        getAppSecretBytes() {
      java.lang.Object ref = appSecret_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appSecret_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appName = 4;
    public static final int APPNAME_FIELD_NUMBER = 4;
    private java.lang.Object appName_;
    /**
     * <code>optional string appName = 4;</code>
     */
    public boolean hasAppName() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string appName = 4;</code>
     */
    public java.lang.String getAppName() {
      java.lang.Object ref = appName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appName = 4;</code>
     */
    public com.google.protobuf.ByteString
        getAppNameBytes() {
      java.lang.Object ref = appName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appAlias = 5;
    public static final int APPALIAS_FIELD_NUMBER = 5;
    private java.lang.Object appAlias_;
    /**
     * <code>optional string appAlias = 5;</code>
     */
    public boolean hasAppAlias() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional string appAlias = 5;</code>
     */
    public java.lang.String getAppAlias() {
      java.lang.Object ref = appAlias_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appAlias_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appAlias = 5;</code>
     */
    public com.google.protobuf.ByteString
        getAppAliasBytes() {
      java.lang.Object ref = appAlias_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appAlias_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appTopic = 6;
    public static final int APPTOPIC_FIELD_NUMBER = 6;
    private java.lang.Object appTopic_;
    /**
     * <code>optional string appTopic = 6;</code>
     */
    public boolean hasAppTopic() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional string appTopic = 6;</code>
     */
    public java.lang.String getAppTopic() {
      java.lang.Object ref = appTopic_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appTopic_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appTopic = 6;</code>
     */
    public com.google.protobuf.ByteString
        getAppTopicBytes() {
      java.lang.Object ref = appTopic_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appTopic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;
    public static final int CHANNELINFOS_FIELD_NUMBER = 7;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo> channelInfos_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo> getChannelInfosList() {
      return channelInfos_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder> 
        getChannelInfosOrBuilderList() {
      return channelInfos_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    public int getChannelInfosCount() {
      return channelInfos_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo getChannelInfos(int index) {
      return channelInfos_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder getChannelInfosOrBuilder(
        int index) {
      return channelInfos_.get(index);
    }

    private void initFields() {
      id_ = 0L;
      appId_ = "";
      appSecret_ = "";
      appName_ = "";
      appAlias_ = "";
      appTopic_ = "";
      channelInfos_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getAppSecretBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getAppNameBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getAppAliasBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getAppTopicBytes());
      }
      for (int i = 0; i < channelInfos_.size(); i++) {
        output.writeMessage(7, channelInfos_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getAppSecretBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getAppNameBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getAppAliasBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getAppTopicBytes());
      }
      for (int i = 0; i < channelInfos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, channelInfos_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameAppInfo}
     *
     * <pre>
     *平台颁发给业务的信息表
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getChannelInfosFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        appSecret_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        appName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        appAlias_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        appTopic_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        if (channelInfosBuilder_ == null) {
          channelInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
        } else {
          channelInfosBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.appSecret_ = appSecret_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.appName_ = appName_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.appAlias_ = appAlias_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.appTopic_ = appTopic_;
        if (channelInfosBuilder_ == null) {
          if (((bitField0_ & 0x00000040) == 0x00000040)) {
            channelInfos_ = java.util.Collections.unmodifiableList(channelInfos_);
            bitField0_ = (bitField0_ & ~0x00000040);
          }
          result.channelInfos_ = channelInfos_;
        } else {
          result.channelInfos_ = channelInfosBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasAppSecret()) {
          bitField0_ |= 0x00000004;
          appSecret_ = other.appSecret_;
          onChanged();
        }
        if (other.hasAppName()) {
          bitField0_ |= 0x00000008;
          appName_ = other.appName_;
          onChanged();
        }
        if (other.hasAppAlias()) {
          bitField0_ |= 0x00000010;
          appAlias_ = other.appAlias_;
          onChanged();
        }
        if (other.hasAppTopic()) {
          bitField0_ |= 0x00000020;
          appTopic_ = other.appTopic_;
          onChanged();
        }
        if (channelInfosBuilder_ == null) {
          if (!other.channelInfos_.isEmpty()) {
            if (channelInfos_.isEmpty()) {
              channelInfos_ = other.channelInfos_;
              bitField0_ = (bitField0_ & ~0x00000040);
            } else {
              ensureChannelInfosIsMutable();
              channelInfos_.addAll(other.channelInfos_);
            }
            onChanged();
          }
        } else {
          if (!other.channelInfos_.isEmpty()) {
            if (channelInfosBuilder_.isEmpty()) {
              channelInfosBuilder_.dispose();
              channelInfosBuilder_ = null;
              channelInfos_ = other.channelInfos_;
              bitField0_ = (bitField0_ & ~0x00000040);
              channelInfosBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getChannelInfosFieldBuilder() : null;
            } else {
              channelInfosBuilder_.addAllMessages(other.channelInfos_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 2;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional string appSecret = 3;
      private java.lang.Object appSecret_ = "";
      /**
       * <code>optional string appSecret = 3;</code>
       */
      public boolean hasAppSecret() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string appSecret = 3;</code>
       */
      public java.lang.String getAppSecret() {
        java.lang.Object ref = appSecret_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appSecret_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appSecret = 3;</code>
       */
      public com.google.protobuf.ByteString
          getAppSecretBytes() {
        java.lang.Object ref = appSecret_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appSecret_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appSecret = 3;</code>
       */
      public Builder setAppSecret(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appSecret_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appSecret = 3;</code>
       */
      public Builder clearAppSecret() {
        bitField0_ = (bitField0_ & ~0x00000004);
        appSecret_ = getDefaultInstance().getAppSecret();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appSecret = 3;</code>
       */
      public Builder setAppSecretBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        appSecret_ = value;
        onChanged();
        return this;
      }

      // optional string appName = 4;
      private java.lang.Object appName_ = "";
      /**
       * <code>optional string appName = 4;</code>
       */
      public boolean hasAppName() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string appName = 4;</code>
       */
      public java.lang.String getAppName() {
        java.lang.Object ref = appName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appName = 4;</code>
       */
      public com.google.protobuf.ByteString
          getAppNameBytes() {
        java.lang.Object ref = appName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appName = 4;</code>
       */
      public Builder setAppName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        appName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appName = 4;</code>
       */
      public Builder clearAppName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        appName_ = getDefaultInstance().getAppName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appName = 4;</code>
       */
      public Builder setAppNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        appName_ = value;
        onChanged();
        return this;
      }

      // optional string appAlias = 5;
      private java.lang.Object appAlias_ = "";
      /**
       * <code>optional string appAlias = 5;</code>
       */
      public boolean hasAppAlias() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional string appAlias = 5;</code>
       */
      public java.lang.String getAppAlias() {
        java.lang.Object ref = appAlias_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appAlias_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appAlias = 5;</code>
       */
      public com.google.protobuf.ByteString
          getAppAliasBytes() {
        java.lang.Object ref = appAlias_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appAlias_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appAlias = 5;</code>
       */
      public Builder setAppAlias(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        appAlias_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appAlias = 5;</code>
       */
      public Builder clearAppAlias() {
        bitField0_ = (bitField0_ & ~0x00000010);
        appAlias_ = getDefaultInstance().getAppAlias();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appAlias = 5;</code>
       */
      public Builder setAppAliasBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        appAlias_ = value;
        onChanged();
        return this;
      }

      // optional string appTopic = 6;
      private java.lang.Object appTopic_ = "";
      /**
       * <code>optional string appTopic = 6;</code>
       */
      public boolean hasAppTopic() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional string appTopic = 6;</code>
       */
      public java.lang.String getAppTopic() {
        java.lang.Object ref = appTopic_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appTopic_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appTopic = 6;</code>
       */
      public com.google.protobuf.ByteString
          getAppTopicBytes() {
        java.lang.Object ref = appTopic_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appTopic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appTopic = 6;</code>
       */
      public Builder setAppTopic(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        appTopic_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appTopic = 6;</code>
       */
      public Builder clearAppTopic() {
        bitField0_ = (bitField0_ & ~0x00000020);
        appTopic_ = getDefaultInstance().getAppTopic();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appTopic = 6;</code>
       */
      public Builder setAppTopicBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        appTopic_ = value;
        onChanged();
        return this;
      }

      // repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo> channelInfos_ =
        java.util.Collections.emptyList();
      private void ensureChannelInfosIsMutable() {
        if (!((bitField0_ & 0x00000040) == 0x00000040)) {
          channelInfos_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo>(channelInfos_);
          bitField0_ |= 0x00000040;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder> channelInfosBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo> getChannelInfosList() {
        if (channelInfosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(channelInfos_);
        } else {
          return channelInfosBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public int getChannelInfosCount() {
        if (channelInfosBuilder_ == null) {
          return channelInfos_.size();
        } else {
          return channelInfosBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo getChannelInfos(int index) {
        if (channelInfosBuilder_ == null) {
          return channelInfos_.get(index);
        } else {
          return channelInfosBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder setChannelInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo value) {
        if (channelInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelInfosIsMutable();
          channelInfos_.set(index, value);
          onChanged();
        } else {
          channelInfosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder setChannelInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder builderForValue) {
        if (channelInfosBuilder_ == null) {
          ensureChannelInfosIsMutable();
          channelInfos_.set(index, builderForValue.build());
          onChanged();
        } else {
          channelInfosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder addChannelInfos(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo value) {
        if (channelInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelInfosIsMutable();
          channelInfos_.add(value);
          onChanged();
        } else {
          channelInfosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder addChannelInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo value) {
        if (channelInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelInfosIsMutable();
          channelInfos_.add(index, value);
          onChanged();
        } else {
          channelInfosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder addChannelInfos(
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder builderForValue) {
        if (channelInfosBuilder_ == null) {
          ensureChannelInfosIsMutable();
          channelInfos_.add(builderForValue.build());
          onChanged();
        } else {
          channelInfosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder addChannelInfos(
          int index, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder builderForValue) {
        if (channelInfosBuilder_ == null) {
          ensureChannelInfosIsMutable();
          channelInfos_.add(index, builderForValue.build());
          onChanged();
        } else {
          channelInfosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder addAllChannelInfos(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo> values) {
        if (channelInfosBuilder_ == null) {
          ensureChannelInfosIsMutable();
          super.addAll(values, channelInfos_);
          onChanged();
        } else {
          channelInfosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder clearChannelInfos() {
        if (channelInfosBuilder_ == null) {
          channelInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
        } else {
          channelInfosBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public Builder removeChannelInfos(int index) {
        if (channelInfosBuilder_ == null) {
          ensureChannelInfosIsMutable();
          channelInfos_.remove(index);
          onChanged();
        } else {
          channelInfosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder getChannelInfosBuilder(
          int index) {
        return getChannelInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder getChannelInfosOrBuilder(
          int index) {
        if (channelInfosBuilder_ == null) {
          return channelInfos_.get(index);  } else {
          return channelInfosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder> 
           getChannelInfosOrBuilderList() {
        if (channelInfosBuilder_ != null) {
          return channelInfosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(channelInfos_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder addChannelInfosBuilder() {
        return getChannelInfosFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder addChannelInfosBuilder(
          int index) {
        return getChannelInfosFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.ChannelInfo channelInfos = 7;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder> 
           getChannelInfosBuilderList() {
        return getChannelInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder> 
          getChannelInfosFieldBuilder() {
        if (channelInfosBuilder_ == null) {
          channelInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfoOrBuilder>(
                  channelInfos_,
                  ((bitField0_ & 0x00000040) == 0x00000040),
                  getParentForChildren(),
                  isClean());
          channelInfos_ = null;
        }
        return channelInfosBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameAppInfo)
    }

    static {
      defaultInstance = new GameAppInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameAppInfo)
  }

  public interface RequestGetAppCallbackOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAppCallback}
   *
   * <pre>
   * GameAppService.java
   * 获取App回调配置信息
   * domain = 4302, op = 30
   * </pre>
   */
  public static final class RequestGetAppCallback extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAppCallbackOrBuilder {
    // Use RequestGetAppCallback.newBuilder() to construct.
    private RequestGetAppCallback(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAppCallback(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAppCallback defaultInstance;
    public static RequestGetAppCallback getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAppCallback getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAppCallback(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAppCallback> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAppCallback>() {
      public RequestGetAppCallback parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAppCallback(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAppCallback> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAppCallback}
     *
     * <pre>
     * GameAppService.java
     * 获取App回调配置信息
     * domain = 4302, op = 30
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallbackOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam param_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppCallbackParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAppCallback)
    }

    static {
      defaultInstance = new RequestGetAppCallback(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAppCallback)
  }

  public interface ResponseGetAppCallbackOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
     */
    boolean hasAppConfig();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback getAppConfig();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder getAppConfigOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppCallback}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = app不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetAppCallback extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAppCallbackOrBuilder {
    // Use ResponseGetAppCallback.newBuilder() to construct.
    private ResponseGetAppCallback(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAppCallback(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAppCallback defaultInstance;
    public static ResponseGetAppCallback getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAppCallback getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAppCallback(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = appConfig_.toBuilder();
              }
              appConfig_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(appConfig_);
                appConfig_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAppCallback> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAppCallback>() {
      public ResponseGetAppCallback parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAppCallback(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAppCallback> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;
    public static final int APPCONFIG_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback appConfig_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
     */
    public boolean hasAppConfig() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback getAppConfig() {
      return appConfig_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder getAppConfigOrBuilder() {
      return appConfig_;
    }

    private void initFields() {
      appConfig_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasAppConfig()) {
        if (!getAppConfig().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, appConfig_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, appConfig_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppCallback}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = app不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallbackOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getAppConfigFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (appConfigBuilder_ == null) {
          appConfig_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance();
        } else {
          appConfigBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (appConfigBuilder_ == null) {
          result.appConfig_ = appConfig_;
        } else {
          result.appConfig_ = appConfigBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback.getDefaultInstance()) return this;
        if (other.hasAppConfig()) {
          mergeAppConfig(other.getAppConfig());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasAppConfig()) {
          if (!getAppConfig().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;
      private fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback appConfig_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder> appConfigBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public boolean hasAppConfig() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback getAppConfig() {
        if (appConfigBuilder_ == null) {
          return appConfig_;
        } else {
          return appConfigBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public Builder setAppConfig(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback value) {
        if (appConfigBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          appConfig_ = value;
          onChanged();
        } else {
          appConfigBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public Builder setAppConfig(
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder builderForValue) {
        if (appConfigBuilder_ == null) {
          appConfig_ = builderForValue.build();
          onChanged();
        } else {
          appConfigBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public Builder mergeAppConfig(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback value) {
        if (appConfigBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              appConfig_ != fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance()) {
            appConfig_ =
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.newBuilder(appConfig_).mergeFrom(value).buildPartial();
          } else {
            appConfig_ = value;
          }
          onChanged();
        } else {
          appConfigBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public Builder clearAppConfig() {
        if (appConfigBuilder_ == null) {
          appConfig_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.getDefaultInstance();
          onChanged();
        } else {
          appConfigBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder getAppConfigBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getAppConfigFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder getAppConfigOrBuilder() {
        if (appConfigBuilder_ != null) {
          return appConfigBuilder_.getMessageOrBuilder();
        } else {
          return appConfig_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.AppCallback appConfig = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder> 
          getAppConfigFieldBuilder() {
        if (appConfigBuilder_ == null) {
          appConfigBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallbackOrBuilder>(
                  appConfig_,
                  getParentForChildren(),
                  isClean());
          appConfig_ = null;
        }
        return appConfigBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppCallback)
    }

    static {
      defaultInstance = new ResponseGetAppCallback(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppCallback)
  }

  public interface RequestGetAppInfoByAppIdOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string appId = 1;
    /**
     * <code>optional string appId = 1;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 1;</code>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 1;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAppInfoByAppId}
   *
   * <pre>
   * GameAppService.java
   * 根据AppId获取游戏信息
   * domain = 4302, op = 31
   * </pre>
   */
  public static final class RequestGetAppInfoByAppId extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAppInfoByAppIdOrBuilder {
    // Use RequestGetAppInfoByAppId.newBuilder() to construct.
    private RequestGetAppInfoByAppId(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAppInfoByAppId(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAppInfoByAppId defaultInstance;
    public static RequestGetAppInfoByAppId getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAppInfoByAppId getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAppInfoByAppId(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAppInfoByAppId> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAppInfoByAppId>() {
      public RequestGetAppInfoByAppId parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAppInfoByAppId(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAppInfoByAppId> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 1;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string appId = 1;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAppInfoByAppId}
     *
     * <pre>
     * GameAppService.java
     * 根据AppId获取游戏信息
     * domain = 4302, op = 31
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppIdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 1;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAppInfoByAppId)
    }

    static {
      defaultInstance = new RequestGetAppInfoByAppId(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAppInfoByAppId)
  }

  public interface ResponseGetAppInfoByAppIdOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
     */
    boolean hasGameAppInfo();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo getGameAppInfo();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder getGameAppInfoOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppInfoByAppId}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = app不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetAppInfoByAppId extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAppInfoByAppIdOrBuilder {
    // Use ResponseGetAppInfoByAppId.newBuilder() to construct.
    private ResponseGetAppInfoByAppId(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAppInfoByAppId(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAppInfoByAppId defaultInstance;
    public static ResponseGetAppInfoByAppId getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAppInfoByAppId getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAppInfoByAppId(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = gameAppInfo_.toBuilder();
              }
              gameAppInfo_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(gameAppInfo_);
                gameAppInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAppInfoByAppId> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAppInfoByAppId>() {
      public ResponseGetAppInfoByAppId parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAppInfoByAppId(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAppInfoByAppId> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;
    public static final int GAMEAPPINFO_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo gameAppInfo_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
     */
    public boolean hasGameAppInfo() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo getGameAppInfo() {
      return gameAppInfo_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder getGameAppInfoOrBuilder() {
      return gameAppInfo_;
    }

    private void initFields() {
      gameAppInfo_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, gameAppInfo_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameAppInfo_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppInfoByAppId}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = app不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppIdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.class, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameAppInfoFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameAppInfoBuilder_ == null) {
          gameAppInfo_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance();
        } else {
          gameAppInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId build() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId result = new fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (gameAppInfoBuilder_ == null) {
          result.gameAppInfo_ = gameAppInfo_;
        } else {
          result.gameAppInfo_ = gameAppInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId.getDefaultInstance()) return this;
        if (other.hasGameAppInfo()) {
          mergeGameAppInfo(other.getGameAppInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;
      private fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo gameAppInfo_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder> gameAppInfoBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public boolean hasGameAppInfo() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo getGameAppInfo() {
        if (gameAppInfoBuilder_ == null) {
          return gameAppInfo_;
        } else {
          return gameAppInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public Builder setGameAppInfo(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo value) {
        if (gameAppInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gameAppInfo_ = value;
          onChanged();
        } else {
          gameAppInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public Builder setGameAppInfo(
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder builderForValue) {
        if (gameAppInfoBuilder_ == null) {
          gameAppInfo_ = builderForValue.build();
          onChanged();
        } else {
          gameAppInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public Builder mergeGameAppInfo(fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo value) {
        if (gameAppInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              gameAppInfo_ != fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance()) {
            gameAppInfo_ =
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.newBuilder(gameAppInfo_).mergeFrom(value).buildPartial();
          } else {
            gameAppInfo_ = value;
          }
          onChanged();
        } else {
          gameAppInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public Builder clearGameAppInfo() {
        if (gameAppInfoBuilder_ == null) {
          gameAppInfo_ = fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.getDefaultInstance();
          onChanged();
        } else {
          gameAppInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder getGameAppInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getGameAppInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder getGameAppInfoOrBuilder() {
        if (gameAppInfoBuilder_ != null) {
          return gameAppInfoBuilder_.getMessageOrBuilder();
        } else {
          return gameAppInfo_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameAppInfo gameAppInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder> 
          getGameAppInfoFieldBuilder() {
        if (gameAppInfoBuilder_ == null) {
          gameAppInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo.Builder, fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfoOrBuilder>(
                  gameAppInfo_,
                  getParentForChildren(),
                  isClean());
          gameAppInfo_ = null;
        }
        return gameAppInfoBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppInfoByAppId)
    }

    static {
      defaultInstance = new ResponseGetAppInfoByAppId(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppInfoByAppId)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027protocol_game_app.proto\022-fm.lizhi.comm" +
      "ons.template.datacenter.protocol\"2\n\023GetA" +
      "ppCallbackParam\022\r\n\005appId\030\001 \002(\t\022\014\n\004type\030\002" +
      " \001(\005\"[\n\013AppCallback\022\r\n\005appId\030\001 \002(\t\022\014\n\004ty" +
      "pe\030\002 \002(\005\022\023\n\013callbackKey\030\003 \002(\t\022\032\n\022callbac" +
      "kGetUserUrl\030\004 \002(\t\"q\n\013ChannelInfo\022\n\n\002id\030\001" +
      " \001(\003\022\017\n\007channel\030\002 \001(\t\022\024\n\014channelAppId\030\003 " +
      "\001(\t\022\025\n\rchannelAppKey\030\004 \001(\t\022\030\n\020channelApp" +
      "Secret\030\005 \001(\t\"\302\001\n\013GameAppInfo\022\n\n\002id\030\001 \001(\003" +
      "\022\r\n\005appId\030\002 \001(\t\022\021\n\tappSecret\030\003 \001(\t\022\017\n\007ap",
      "pName\030\004 \001(\t\022\020\n\010appAlias\030\005 \001(\t\022\020\n\010appTopi" +
      "c\030\006 \001(\t\022P\n\014channelInfos\030\007 \003(\0132:.fm.lizhi" +
      ".commons.template.datacenter.protocol.Ch" +
      "annelInfo\"j\n\025RequestGetAppCallback\022Q\n\005pa" +
      "ram\030\001 \001(\0132B.fm.lizhi.commons.template.da" +
      "tacenter.protocol.GetAppCallbackParam\"g\n" +
      "\026ResponseGetAppCallback\022M\n\tappConfig\030\001 \001" +
      "(\0132:.fm.lizhi.commons.template.datacente" +
      "r.protocol.AppCallback\")\n\030RequestGetAppI" +
      "nfoByAppId\022\r\n\005appId\030\001 \001(\t\"l\n\031ResponseGet",
      "AppInfoByAppId\022O\n\013gameAppInfo\030\001 \001(\0132:.fm" +
      ".lizhi.commons.template.datacenter.proto" +
      "col.GameAppInfoB5\n\034fm.lizhi.ocean.seal.p" +
      "rotocolB\023GameAppServiceProtoH\001"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppCallbackParam_descriptor,
              new java.lang.String[] { "AppId", "Type", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_AppCallback_descriptor,
              new java.lang.String[] { "AppId", "Type", "CallbackKey", "CallbackGetUserUrl", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelInfo_descriptor,
              new java.lang.String[] { "Id", "Channel", "ChannelAppId", "ChannelAppKey", "ChannelAppSecret", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameAppInfo_descriptor,
              new java.lang.String[] { "Id", "AppId", "AppSecret", "AppName", "AppAlias", "AppTopic", "ChannelInfos", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppCallback_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppCallback_descriptor,
              new java.lang.String[] { "AppConfig", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppInfoByAppId_descriptor,
              new java.lang.String[] { "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppInfoByAppId_descriptor,
              new java.lang.String[] { "GameAppInfo", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
